@charset "utf-8";
/* CSS Document */
body {
    font-family: 'Roboto', arial, sans-serif!important;
    font-size: 14px;
    color: #333;
}

/* Import Fonts
------------------------------------------------------------ */

a {
    color: #333;
}

/* Defaults
------------------------------------------------------------ */

.wrap-page {
    background-color: #fff;
}

.contentMain {
    background-color: #fff;
}

.iconSearchHeader {
    margin-right: 0px !important;
    z-index: 0 !important;
}

.article-title {
    color: #424242;
    font-size: 19px;
    padding-top: 10px;
    font-weight: 600;
    position: relative;
}

.article-detail {
    margin-top: 10px;
    margin-bottom: 30px;
}

.article-detail-about {
    background-color: #fff;
    margin: 20px 0;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #d2dae2;
    overflow: auto;
    font-size: 16px;
}
.article-detail-about p span{
    font-family: 'Roboto', arial, sans-serif!important;
    font-size: 16px!important;
}
/*.article-detail-about p:nth-child(2) span{
    font-size: 22px!important;
    font-weight: bold;
}
.article-detail-about p:nth-child(3) span{
    font-size: 22px!important;
    font-weight: bold;
}
.article-detail-about p:nth-child(2){
    font-size: 22px!important;
    color: #c0392b;
    font-weight: bold;
}
.article-detail-about p:nth-child(3){
    font-size: 22px!important;
    color: #c0392b;
    font-weight: bold;
}*/
.munu-footer a {
    color: #333;
}

h1, h2, h3 {
    margin: 0;
    padding: 0
}

ul, ul li {
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Hyperlinks
------------------------------------------------------------ */

a:link {
    /* color: #333; */
}

a:hover, a:focus, a:active {
    /* color: #333; */
    text-decoration: none;
}

/* Wrap
------------------------------------------------------------ */

@media (min-width: 1200px) {
    .container {
        width: 1170px;
    }
}

/* Header
------------------------------------------------------------ */

/*menu*/
.navbar-default {
        background-color: #fff;
        border-color: transparent !important;
        border-bottom: 2px solid #c3e4fd!important;
    }
@media (max-width: 1023.98){
    .navbar-default {
        background-color: #d92e2e;
    }
}

@media (min-width: 1023px) {
    li.dropdown:hover>ul {
        display: block;
    }
}

.menu-hor .nav>li>a {
    position: relative;
    display: block;
    text-transform: uppercase;
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
    font-size: 14px;
    padding: 9px 20px 7px;
}

.dropdown-menu>li>a {
    display: block;
    padding: 8px 20px;
    font-weight: 500;
    color: #333!important;
}

@media (min-width: 767px) {
    .navbar {
        border-radius: 0px;
    }
}

.navbar {
    position: relative;
    min-height: 35px;
}

.dmsp .dropdown-menu {
    border: none;
    width: 250px;
    z-index: 999999;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}

.dropdown-submenu>.dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    border-radius: 0!important;
    /*height: 330px;*/
    /*min-height: 250px;*/
    width: 250px;
    background: #FFF;
}

.dmsp ul li a {
    border-bottom: 1px solid #eee;
    text-transform: uppercase;
    padding: 6px 10px 6px 15px;
    font-size: 14px;
    text-transform: capitalize;
    font-weight: 400;
    color: #333;
}

.navbar {
    margin-bottom: 0px;
}

/*end menu*/

/*.top-header{
    background-color: #31b142;
}*/

.top-header {
    height: 64px;
    background: #950000;
    background-image: linear-gradient(150deg, #950000, #f40000);
    /**background-image: linear-gradient(150deg, #0066b3, #2e85c8);**/
}

.top-header .nav>li>a:hover, .top-header .nav>li>a:active, .top-header .nav>li>a:focus {
    background: none!important;
}

.top-header .nav>li>a {
    position: relative;
    display: block;
    padding: 7px 20px;
    margin-top: 2px;
    color: #fff;
    font-size: 14px;
}

.top-header .nav>li>a:hover, .top-header .nav>li>a:active {
    color: #fff
}

/*.li-tb:before{
    content:'\f0f3';
    font-family: 'FontAwesome';
    position: absolute;
    margin-top: 8px;
    color:#fff;   
}*/

.li-tg:before {
    content: '\f059';
    font-family: 'FontAwesome';
    position: absolute;
    margin-top: 8px;
    color: #fff;
}

.block-mobile.regis li.li-tg:before {
    font-family: 'FontAwesome';
    position: absolute;
    color: #fff;
    padding-right: 8px;
    width: 10px;
    margin-top: 0
}

.li-dn:before {
    content: '\f090';
    font-family: 'FontAwesome';
    position: absolute;
    color: #fff;
    margin-top: 8px;
    margin-right: 5px;
}

.li-dk:before {
    content: '\f007';
    font-family: 'FontAwesome';
    position: absolute;
    color: #fff;
    margin-top: 8px;
    padding-left: 0;
    margin-right: 5px;
}

.li-ql:before {
    content: '\f0ce';
    font-family: 'FontAwesome';
    position: absolute;
    margin-top: 8px;
    color: #fff;
}

.li-download:before {
    content: '\f019';
    font-family: 'FontAwesome';
    position: absolute;
    margin-top: 8px;
    color: #fff;
}

.li-shopcard:before {
    content: '\f07a';
    font-family: 'FontAwesome';
    position: absolute;
    margin-top: 8px;
    color: #fff;
}

.banner {
    background-color: #fff;
    padding: 10px 0;
    position: relative;
}

.navbar-form {
    margin-top: 5px!important;
}

.search-form {
    /* height: 30px; */
    border-radius: 0px;
    /* background-color: #fff; */
    position: relative;
    margin-top: 14px;
    border: 1px solid #fff;
    display: inline-block;
    opacity: 0.7;
}

.nav-search {
    width: 30%;
    display: none;
    height: 64px;
}

.nav-right-user {
    margin-top: 14px;
}

.search-form input {
    border: none;
    /* height: 30px; */
    margin-top: 1px;
    margin-left: 2px;
    border-radius: 0px;
    background-color: transparent;
}

.search-form input {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 14px;
    outline: none;
    color: #ffff;
}

.search-form button {
    background-color: #bd0000;
    height: 23px;
    border: none;
    right: -1px;
    top: -1px;
    border-radius: 0;
    border-right: 1px solid #fff;
}

::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: #ffff;
}

.fa-search {
    font-size: 16px;
    color: #fff;
}

.card-wrapper {
    height: 40px;
    text-align: center;
    margin-top: 14px;
}

.dmsp {
    width: 250px;
    background-color: #f2f2f2;
}

.fa-list {
    margin-right: 10px;
}

@media (max-width: 1023px) {
    .logo {
        width: 220px;
    }
}

/*mobile-header*/

.navbar-toggle {
    float: left;
    left: 15px;
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 7px 6px;
    background-color: #fff;
    border-color: #fff;
}

.regis {
    float: right;
}

.regis li {
    float: left;
    padding: 15px 15px;
}

.navbar-default .navbar-nav>li>a {
    color: #333;
}

@media (max-width:1023.98px) {
    .none-mobile {
        display: none;
    }
    .navbar {
        position: fixed;
        height: 45px;
        width: 100%;
        top: 0;
        z-index: 1000;
        background-image: linear-gradient( 150deg , #950000, #f40000);
    }
    /* .banner {
        top: 46px;
        padding-bottom: 0;
    } */
    .card-wrapper {
        position: absolute;
        top: -140px;
        right: 20px
    }
    .wrap-search {
        padding: 0px 15px 0 15px;
    }
    .search-form {
        margin-top: 0
    }
    .dmsp {
        width: 100%;
        background-color: #fff;
    }
    .fa-list {
        display: none;
    }
    .navbar-default .navbar-nav .open .dropdown-menu>li>a {
        color: #333;
    }
    .navbar-nav .open .dropdown-menu .dropdown-header, .navbar-nav .open .dropdown-menu>li>a {
        padding: 10px 15px 10px 25px;
    }
    /* header {
        height: 174px;
    } */
}

@media (min-width: 1024px) {
    .block-mobile {
        display: none;
    }
}

.block-mobile.regis>li>a {
    position: relative;
    display: block;
    padding: 0px 0px 0 20px;
    float: left;
    color: #fff!important;
    font-family: 'Roboto', arial, sans-serif!important;
}

/*mobile-menu cho ipad*/

@media (min-width: 767px) and (max-width: 1023px) {
    .navbar-collapse.collapse {
        display: none!important;
        height: auto!important;
        padding-bottom: 0;
        overflow: auto!important;
    }
    .navbar-toggle {
        display: block;
    }
    .collapse.in {
        display: block!important;
        background-color: #fff;
    }
    .navbar-nav>li {
        float: none;
    }
    .navbar-header {
        float: left;
        width: 100%;
    }
    .container {
        width: 100%
    }
    .navbar-collapse {
        border-top: 1px solid transparent;
    }
    .navbar-nav {
        width: 100%
    }
    .dropdown-menu {
        width: 100%;
    }
    .navbar-nav .open .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
    .navbar-nav {
        margin: 7.5px 0;
    }
}

/*end mobile-menu cho ipad*/

/*----------------------------------------------end header-------------------------------*/

/*-----------------------------------------------Slide---------------------------------*/

.carousel-control.left, .carousel-control.right {
    background-image: none;
}

.carousel-inner img {
    margin: auto;
}

/*---------------------------Slick product-----*/

.box-product {
    margin-top: 30px;
}

.wrap-th .item {
    padding: 5px 5px;
    border-radius: 5px;
}

.wrap-th .col-md-2 .logoncc {
    border: 3px solid #f4f4f4;
    border-radius: 5px;
    height: 120px;
    background: #fff;
    display: block;
    position: relative;
}

.wrap-th .col-md-2 .logoncc img {
    display: block;
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    margin: auto;
    max-height: 100%;
    max-width: 100%;
    padding: 0px 20px;
}

.wrap-th .col-md-2 .logoncc img:hover {
    transform: scale(1.05);
}

.box-header {
    text-transform: uppercase;
    margin: 0 0 10px;
    text-align: left;
    position: relative;
    /*border-bottom: 1px dotted #ddd;*/
    /*font-family: Roboto-Regular;*/
    font-size: 20px;
    font-weight: 500;
    color: #424242;
    display: -webkit-flex;
    /* Safari */
    display: flex;
    -webkit-flex-direction: row;
    /* Safari */
    flex-direction: row;
    -webkit-justify-content: space-between;
    /* Safari */
    justify-content: space-between;
    -webkit-flex-wrap: wrap;
    /* Safari */
    flex-wrap: wrap;
}

.box-header .icon, .box-header-km .icon {
    display: block;
    width: 40px;
    min-height: 24px;
    position: absolute;
    left: 50%;
    margin-left: -15px;
    font-size: 24px;
    background: #fff;
    text-align: center;
    bottom: -15px;
    color: #31b142;
}

.tit-thumb-product {
    font-size: 13px;
    line-height: 1.5;
    margin-top: 5px;
    min-height: 40px;
}

.price {
    font-weight: 500;
    font-size: 14px;
    color: #5aa32a;
}

.swiper-container-horizontal {
    background-color: #fff;
    padding-bottom: 10px;
    margin-top: 20px;
}

.swiper-container-horizontal .slider .slick-list .slick-slide {
    margin: 0px 15px;
}

.ads {
    margin-top: 20px;
}

.box-product2 {
    /*    background-color: #fff;*/
    margin-top: 5px;
}

.box-product2 .thumb-product {
    background-color: #fff;
    /*border:1px solid #eee;*/
}

.box-product2 .thumb-product .info-pro {
    padding: 0 10px 10px;
}

.wrap-product {
    background-color: #fff;
    padding: 15px;
    padding-bottom: 0px;
}

/*--------------------------------------FOOTER------------------------------------------------*/

.form-email {
    background: #fff;
    border-radius: 25px;
    height: 40px;
    margin-top: 10px;
    margin-bottom: 20px;
}

.form-email input {
    border-radius: 25px;
    height: 40px;
    border: none;
    padding-left: 10px;
    width: 88%;
}

.form-email button.newsletter_button {
    background: #fff;
    border-radius: 0 25px 25px 0;
    border: none;
    width: 10%;
}

.fa-envelope {
    color: #31b142;
}

.bg-top-ft {
    background: #31b142;
    min-height: 245px;
}

.top-ft:before {
    content: url(images/qua_03.png);
    position: absolute;
    left: 200px;
}

.ft-before {
    background: #fafafa;
    border-top: 1px solid #eee;
}

.top-ft:after {
    content: url(images/qua_05.png);
    position: absolute;
    right: 200px;
}

.social.new-letter {
    padding-top: 30px;
}

.new-letter p {
    text-align: center;
    color: #fff;
    font-size: 16px;
    text-transform: uppercase;
}

.social-icon {
    text-align: center;
    color: #fff;
    font-size: 36px;
}

.social-icon a {
    color: #fff;
}

.widget-title {
    color: #424242;
    font-size: 17px;
    margin-bottom: 15px;
    position: relative;
    text-transform: uppercase;
    font-weight: 400 !important;
}

.widget-title:after {
    content: '';
    height: 1px;
    width: 50px;
    position: absolute;
    background: #f37021;
    bottom: -12px;
    left: 0px;
}

.widget-menu li {
    font-size: 14px;
    padding: 6px 0;
    padding-left: 15px;
}

.widget-menu li:before {
    position: absolute;
    left: 0;
    content: '•';
    color: #f37021;
    margin-left: 15px;
}

.widget {
    margin-top: 20px;
    margin-bottom: 20px;
}

.bg-ft-before {
    position: relative;
}

.bg-top-ft .container:after {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: url(images/curved-bg.png) no-repeat bottom center;
    height: 30px;
    width: 100%;
}

.wrap-top-ft {
    position: relative;
}

.summary {
    margin-top: 15px;
}

.end-ft {
    letter-spacing: 1;
    text-align: center;
    color: #fff;
    padding: 5px 15px;
    background-color: #5aa32a;
    /*padding-top:15px;*/
}

.end-ft span {
    padding-left: 10px;
    padding-right: 10px;
}

.hotline-ft, .email-ft {
    position: relative;
    margin-left: 20px;
}

.hotline-ft:before {
    content: '\f098';
    font-family: 'FontAwesome';
    position: absolute;
    color: #fff;
    left: -10px;
}

.email-ft:before {
    content: '\f0e0';
    font-family: 'FontAwesome';
    position: absolute;
    color: #fff;
    left: -10px;
}

@media (max-width: 1023px) {
    .top-ft:before {
        content: none;
    }
    .top-ft:after {
        content: none;
    }
    .full-home-banners {
        padding-top: 0px!important;
    }
}

.banner-demand {
    margin-top: 10px!important;
    margin-bottom: 10px!important;
}

.product-category-wrap {
    padding: 30px 0 30px 0;
    background-color: #fff;
    position: relative;
}

.product-left-content {
    height: 320px;
    padding-top: 80px;
    text-align: center;
}

.product-category-wrap .title-black {
    font-size: 18px;
    color: #000;
    font-family: 'Roboto', arial, sans-serif;
    line-height: 32px;
}

.product-category-wrap .product-iton {
    font-size: 50px;
    color: #ffa830;
}

.vtd-sp-nong-nghiep {
    font-family: vitrade !important;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vtd-sp-nong-nghiep:before {
    content: "\E92D";
}

.view-more-link {
    margin-top: 10px;
    display: inline-block;
    color: #ed3237;
    font-family: 'Roboto', arial, sans-serif;
    font-size: 13px;
    line-height: 15px;
}

.view-more:after {
    content: "";
    width: 6px;
    height: 6px;
    top: -1px;
    position: relative;
    margin-left: 7px;
    display: inline-block;
    border-left: 5px solid #ed3237;
    border-right: 5px solid transparent;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
}

.view-more-link a:hover {
    color: #b8312c;
}

.product-left-content p {
    margin: auto !important;
    margin: 0 !important;
    /*padding: 0 !important;*/
}

.view-more {
    margin: auto !important;
}

.clearfix {
    clear: both;
}

.user-image {
    float: left;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    margin-right: 10px;
    margin-top: -2px;
}

.user-menu .dropdown-menu {
    position: absolute;
    right: 0;
    left: auto;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    padding: 1px 0 0 0;
    border-top-width: 0;
    width: 260px;
    box-shadow: none;
    border-color: #eee;
    top: 50px;
}

.user-menu.open>.dropdown-menu {
    display: block!important;
}

.user-menu>.dropdown-menu>li.user-header {
    height: 160px;
    padding: 10px;
    text-align: center;
    background-color: #5ba2dc;
    float: none !important;
    color: #fff;
}

@media (max-width: 991px) {
    .user-menu .dropdown-menu {
        position: absolute;
        right: 5%;
        left: auto;
        border: 1px solid #ddd;
        background: #fff;
    }
}

.user-menu>.dropdown-menu>li.user-header>img {
    z-index: 5;
    height: 90px;
    width: 90px;
    border: 3px solid;
    border-color: transparent;
    border-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
}

.user-menu>.dropdown-menu>li.user-header>p {
    z-index: 5;
    color: #fff;
    font-size: 20px;
    margin: 0 0 10px;
}

.user-menu>.dropdown-menu>li.user-header>p>small {
    display: block;
    font-size: 12px;
}

.user-menu>.dropdown-menu>li.user-body {
    padding: 15px;
    border-bottom: 1px solid #f4f4f4;
    border-top: 1px solid #dddddd;
    width: 100%;
}

.user-menu>.dropdown-menu>li.user-body::before {
    content: " ";
    display: table;
}

.user-menu>.dropdown-menu>li.user-body a {
    color: #444 !important;
}

.user-menu>.dropdown-menu>li.user-footer {
    background-color: #f8f8f8;
    padding: 10px;
    float: left;
    width: 100%;
}

.user-menu>a:hover, user-menu>a:active {
    background: rgba(0, 0, 0, 0.1);
    color: #f6f6f6;
}

.navbar-header .user-menu>a:hover, .navbar-header .user-menu>a:active {
    background: none;
    color: #333
}

.cart-count {
    width: 18px;
    height: 18px;
    position: relative;
    top: -15px;
    align-items: center;
    justify-content: center;
    border-radius: 100px;
    color: #fff;
    margin-left: -8px;
    background: #31b142;
    border: 1px solid #31b142;
    display: none;
}

.btn-post-cart {
    background: #fff;
    outline: none!important;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    display: inline-block;
    color: #000;
}

.btn-post-cart i {
    /*margin-right: 5px;*/
    position: relative;
    font-size: 30px;
    line-height: 40px;
    display: inline-block;
    color: #f37021;
}

.cart-web-hover {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    visibility: hidden;
    position: absolute;
    top: 100%;
    -webkit-transition: visibility 0s;
    transition: visibility 0s;
    -webkit-transition-delay: .2s;
    transition-delay: .2s;
    z-index: 1000;
}

.cart-sp {
    box-shadow: 0 1px 5rem 0 rgba(0, 0, 0, .2);
    border-radius: .2rem;
    overflow: hidden;
    background-color: #fff;
    width: 40rem;
}

.cart-content-title {
    padding-left: 1rem;
    height: 4rem;
    color: rgba(0, 0, 0, .26);
    text-transform: capitalize;
}

.cart-web-hover-item {
    width: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    padding: 1rem;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
}

.cart-web-hover-item:hover {
    background-color: #f8f8f8;
}

.cart-web-item-left {
    background-position: 50%;
    background-size: cover;
    background-repeat: no-repeat;
    width: 4rem;
    height: 4rem;
    border: 1px solid rgba(0, 0, 0, .09);
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}

.cart-web-item-right {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    /*margin-left: 1rem;*/
    overflow: hidden;
}

.cart-web-item-right .v-center {
    padding: 5px !important;
}

.cart-sp-name {
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.cart-sp-price {
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-left: 4rem;
    -webkit-box-align: baseline;
    -webkit-align-items: baseline;
    -moz-box-align: baseline;
    -ms-flex-align: baseline;
    align-items: baseline;
    color: #ff5722;
}

._2BMmIF {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.cart-web-item-footer {
    height: 4rem;
    line-height: 4rem;
    background-color: #fdfdfd;
    text-align: center;
    text-transform: capitalize;
    font-size: 1.2rem;
    padding: 1rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 5px;
}

.navbar__spacer {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -moz-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.btn-solid-primary {
    background: #ff5722;
    color: #fff;
}

.btn-solid-primary:active {
    background: #f25220;
    box-shadow: inset 0 2px 1px 0 rgba(0, 0, 0, .05);
}

/* slide mới */

.full-home-banners {
    padding-top: 15px;
}

.full-home-banners__main-banner {
    padding-left: 15px;
}

.full-home-banners__right-wrapper {}

/*@media (min-width: 992px) {
    .carousel-inner {
        height: 255px !important;
    }
}

.banner-right {
    width: 100%;
    padding-left: 0;
    height: 255px !important;
}*/

.img-right-item {
    float: left;
    width: 100%;
    height: 50%;
    overflow: hidden;
}

.img-right-item:first-child {
    padding-bottom: 5px;
}

.img-right-item:last-child {
    padding-top: 5px;
}

.img-right-item img {
    height: 100% !important;
    width: 100% !important;
}

/* cơ sở sản xuất, kinh doanh tiêu biểu*/

.business-list .business-item {
    background: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    margin-bottom: 10px;
    align-items: center;
    font-size: 13px;
    position: relative;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .05);
    padding: 10px;
    /*padding-right: 10px;*/
}

.business-list .business-item .business-info {
    line-height: 20px;
    color: #757575;
    margin-right: 0px;
    width: 250px;
    height: 100px;
}

.business-logo {
    padding-right: 10px;
}

.business-list .business-item span {
    display: block;
}

.business-list .business-item span.name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

/*  end*/

/*.box-news{
    background-color: #fff;
}*/

.box-news .block-content {
    background-color: #fff;
    padding: 5px 15px;
}

.wrap-connections {
    background-color: #fff;
    margin-bottom: 20px;
}

.box-connections {
    margin-top: 15px;
    background-color: #c8e89d;
    /*border-bottom: 1px solid #cee8ac;*/
    /*box-shadow: 0 2px 5px 0 rgba(0,0,0,.05);*/
}

@media (max-width: 1023px) {
    .mod-card-item {
        background: transparent!important;
        padding: 0!important;
    }
    .mod-card-kncc .col-sm-3.col-xs-4 {
        padding-left: 5px;
        padding-right: 5px;
    }
    .btn-post-cart i {
        top: 18px;
    }
}

.head-buy-index {
    padding: 0 15px;
}

.h3-head-buy-index {
    font-size: 18px;
    text-transform: uppercase;
    padding-top: 12px;
}

.card-channels-link.canmua-card, .card-channels-link.canban-card, .card-channels-link.timdoitac-card {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
    cursor: pointer;
}

.card-channels-name {
    font-size: 16px;
    padding-left: 15px;
    padding-top: 5px;
}

.card-channels-symbol img {
    width: 33px;
    height: 33px;
}

.wrap-connections.row {
    margin: 0px -7px !important;
}

.buy-left-index {
    padding: 10px;
}

.wrap-buy-li .post-link {
    font-size: 14px;
    line-height: 1.35;
    margin-top: 0;
    margin-bottom: 0;
    font-weight: 500;
}

.news-buy-index {
    display: flex;
    align-items: top;
    align-content: top;
    justify-content: flex-start;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.buy-center-linked-list-columns-index {
    background-color: #fff;
    margin-top: 7px;
    padding: 10px 10px;
    margin-bottom: 0px;
}

.item-news-index {
    display: flex;
    justify-content: flex-start;
    border-bottom: 1px solid #eee;
    padding-top: 5px;
    padding-bottom: 5px;
}

.item-news-index:last-child {
    border-bottom: none;
}

.news-left-index {
    width: 80px;
    height: 60px;
}

.news-right-index {
    width: 100%;
    padding-left: 10px;
}

.h3-news-index {
    font-size: 18px;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: 5px;
    padding-bottom: 8px;
    border-bottom: 1px solid #5aa32a;
    color: #5aa32a;
}

.youtobe-play {
    display: none;
}

.title_video {
    padding: 10px 0px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    color: #31b142;
}

.conten_video {
    text-align: center;
}

.document-right-index {
    width: 100%;
    /*padding-left: 10px;*/
    font-size: 14px !important;
    /*padding-bottom: 10px;*/
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.document-right-index a {
    color: #333 !important;
}

.document-left-index {
    padding: 5px 8px;
    position: absolute;
    background: #0a6537;
    color: #FFF;
    font-family: "Roboto-Bold";
    border-radius: 100%;
    display: block;
}

.mod-card-item {
    background: #fff;
    border-radius: 50px;
    padding: 3px 8px 3px 3px;
    margin-top: 6px;
}

/*.mod-card-content .mod-card-item .card-channels-link {
    display: -webkit-flex;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    color: #000;
    cursor: pointer;
}
.mod-card-content .mod-card-item .card-channels-symbol {
    padding-right: 10px;
}*/

/*.card-channels-name {
    font-size: 18px;
}*/

.icon-buy-li-index {
    height: 50px;
    width: 50px;
}

.icon-buy-li-index img {
    border-radius: 50%;
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    height: 100%;
    width: 100%;
}

.business-item .img-business {
    display: block;
    width: 30%;
    position: relative;
    overflow: hidden;
    height: 100px;
    border-bottom: none;
}

/* menu mobile */

.checkMenu {
    display: none;
}

label.drop-icon {
    position: absolute;
    right: 0;
    top: 0;
}

@media only screen and (min-width: 992px) {
    label.drop-icon {
        display: none;
    }
}

.zoom-small-image img {
    padding: 5px;
}

.button-report-user {
    color: #ff5722!important;
    background: transparent;
    border: none;
    display: inline-block;
    text-align: left;
}

.buy-li1 {
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    margin-top: 10px;
}

.wrap-buy-li .post-link1 {
    font-size: 14px;
    line-height: 1.35;
    margin-top: 0;
    margin-bottom: 0;
    font-weight: 500;
}

.box-news-col {
    margin-top: 10px;
}

.box-news {
    margin-top: 0px;
}

.pull-left-img-news {
    width: 25%;
}

.media-heading-cc {
    font-size: 14px;
    line-height: 1.4;
    font-weight: 400;
}

#youtube-player img {
    width: 100% !important;
}

.row.is-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

label.error {
    display: inline-block;
    color: red;
}

.social-button .fa-facebook-official {
    font-size: 23px;
    color: #4267b2;
    padding-left: 10px;
}

.social-button .fa-twitter {
    font-size: 23px;
    color: #55acee;
    padding-left: 10px;
}

.zalo-share-button {
    width: 30px !important;
    padding-left: 10px;
}

.menu-vertical-dmsp li .fa {
    float: right;
    right: 15px;
    position: absolute;
    top: 10px;
    cursor: pointer;
    z-index: 9;
    /*width: 50px;*/
}

.dropdown-submenu {
    position: relative;
}

.menu-sub-hor .fa {
    float: right;
    right: 6px;
    position: absolute;
    top: 10px;
    cursor: pointer;
    z-index: 9;
    font-size: 15px;
}

@media (max-width: 1023px) {
    .regis li {
        padding: 9px 6px;
        font-size: 14px;
    }
    .regis li a {
        color: #333!important
    }
    .li-tb:before, .li-tg:before, .li-dn:before, .li-dk:before, .li-ql:before {
        color: #fff;
        position: absolute;
        margin-right: 6px;
        margin-top: 0;
    }
    .thumb-product.item .img img {
        /*width:100%;*/
        height: 100%;
    }
    .menu-sub-hor .dropdown-menu {
        position: inherit;
        top: 100%;
        float: none;
    }
    .menu-sub-hor .fa {
        font-size: 25px;
        right: 20px;
    }
    .menu-vertical-dmsp li .fa {
        font-size: 25px;
    }
    .dmsp .dropdown-menu {
        height: auto;
        width: 100%;
    }
    .dropdown-submenu>.dropdown-menu {
        position: inherit;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
    }
    /* ẩn danh mục sản phẩm bên trái trên giao diện mobile */
    /*    .side-nav-categories{
        display: none;
    }*/
}

.top-header .nav .open>a, .top-header .nav .open>a:focus, .top-header .nav .open>a:hover {
    background-color: transparent;
    border: none;
}

.toolbarUser {
    background-color: #fff;
    padding: 20px;
    font-family: 'Roboto', arial, sans-serif;
    margin-bottom: 0px;
    position: relative;
    z-index: 9999;
}

.nav-tabs-custom .nav-tabs-custom>.nav-tabs {
    border: none;
    background-color: transparent!important;
}

.nav-tabs-custom .tab-content {
    border: none;
}

.nav-tabs-custom .nav-tabs {
    border: none;
    background: none;
}

.nav-tabs-custom .nav-tabs>li>a {
    padding: 0;
    padding-top: 10px;
}

.nav-tabs-custom>.tab-content {
    padding: 0!important;
    margin-top: 15px;
}

@media (max-width: 480px) {
    .col-100 {
        width: 100%;
    }
}

.link-products-index {
    list-style: none;
    margin: 0px;
    /*margin-right: 30px;*/
}

.link-products-index li {
    display: inline-block;
}

.link-products-index li a {
    /*color: ;*/
    position: relative;
    z-index: 999;
    background: #f5f5f5;
    padding-right: 20px;
    font-size: 14px;
    font-weight: 400;
    text-transform: none;
}

.btn-success {
    color: #fff!important;
}

@media (min-width: 998px) {
    .products-new-right {
        width: 100%!important;
    }
}

.content-center {
    padding-bottom: 20px;
}

.shop-info-index {
    display: flex;
    align-items: center;
    height: 3.2rem;
    border-top: 1px dotted #e8e8e8;
    padding-left: 3px;
}

.shop-info-index-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1.1rem;
    color: #828282;
    padding-left: 3px;
}

.shopping-cart-index {
    float: right;
    padding-right: 10px;
}

.shopping-cart-index .fa-shopping-cart {
    font-size: 25px;
    color: #f37021;
}

.thumb-product-list-item {
    padding-right: 10px !important;
    padding-left: 10px !important;
    display: grid;
}

.image-avatar-index {
    border-radius: 50%;
    height: 20px;
    width: 20px;
}

.shop-info-index-avatar {
    width: 25px;
}

.shop-info-avatar {
    width: 20px;
}

.image-avatar-shop-prod {
    border-radius: 50%;
    height: 16px;
    width: 16px;
}

.shop-info-shop-name-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 1rem;
    color: #828282;
    padding-left: 3px;
}

.img-certifications-index {
    max-width: 100% !important;
    width: 18px !important;
    height: 18px !important;
    cursor: pointer;
    border-radius: 50%;
}

.cer-item {
    position: absolute;
    right: 0;
    z-index: 1;
    bottom: 0;
    background-color: rgba(0, 0, 0, .3);
}

.img-item {
    position: relative;
}

.modal {
    width: 100%;
    height: 100%;
    /* background-color: rgba(0, 0, 0, .4); */
}

.close-btn-ads {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    line-height: 2.5rem;
    height: 1.875rem;
    width: 1.875rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: absolute;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background-color: #fff;
    top: -.625rem;
    right: -.625rem;
    border-radius: 1.25rem;
    border: 3px solid #fff;
}

.popup-banner-img {
    max-height: -webkit-calc(100vh - 3.75rem);
    max-height: calc(100vh - 3.75rem);
    max-width: -webkit-calc(100vw - 3.75rem);
    max-width: calc(100vw - 3.75rem);
}

.banner-popup-container {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    overflow: visible;
    max-height: 100%;
    /* max-width: 100%; */
}

#distance {
    z-index: 9999;
    position: absolute;
    left: 50%;
    background-color: #fff;
    font-size: 13pt;
    font-weight: 400;
    top: 6%;
    padding: 7px;
}

.slide-wrapper {
    background: #d01c27;
}
@media (min-width:1025px){
    .slide-wrapper .col-sm-12.col-md-8 {
        padding-right: 0;
        width: 69%!important;
    }
    .col-sm-12.col-md-4.countdown-left{
        width: 31%!important;
        padding-right: 14px;
    }
}    
.flip-clock-wrapper{
    width: 100%!important;
}


.group-event-exam-countdown {
    background: linear-gradient(145deg, rgba(28, 39, 208, 0.85) 0%, rgba(28, 39, 208, 0.6) 100%);
    padding: 70px 0;
    position: relative;
    overflow: hidden;
    min-height: 400px;
}

/* Overlay gradient */
.gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 0%, rgba(0,0,0,0.2) 100%);
    z-index: 1;
}

/* Container cho các hình tròn đối xứng */
.symmetric-circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
}

/* Hình tròn lớn đối xứng */
.large-circle {
    position: absolute;
    width: 300px; /* Kích thước lớn hơn */
    height: 300px;
    background-image: url('images/trong-dong-white.png');
    background-size: contain;
    background-repeat: no-repeat;
    filter: drop-shadow(0 0 15px rgba(255,255,255,0.3));
    opacity: 0.6;
}

/* Hình tròn góc trái trên */
.large-circle.top-left {
    top: -50px;
    left: -50px;
    animation: rotateTopLeft 20s infinite linear;
}

/* Hình tròn góc phải dưới */
.large-circle.bottom-right {
    bottom: -50px;
    right: -50px;
    animation: rotateBottomRight 20s infinite linear;
}

/* Animation cho hình tròn góc trái trên */
@keyframes rotateTopLeft {
    0% {
        transform: rotate(0deg) scale(0.8);
        opacity: 0.4;
    }
    50% {
        transform: rotate(180deg) scale(1.2);
        opacity: 0.6;
    }
    100% {
        transform: rotate(360deg) scale(0.8);
        opacity: 0.4;
    }
}

/* Animation cho hình tròn góc phải dưới */
@keyframes rotateBottomRight {
    0% {
        transform: rotate(360deg) scale(0.8);
        opacity: 0.4;
    }
    50% {
        transform: rotate(180deg) scale(1.2);
        opacity: 0.6;
    }
    100% {
        transform: rotate(0deg) scale(0.8);
        opacity: 0.4;
    }
}

.center-drum {
    z-index: 2;
    position: absolute;
    width: 1400px;
    height: 1400px;
    top: -280px;
    left: 50%;
    margin-left: -700px;
}

.rotating-drum {
    width: 100%;
    height: 100%;
    background: transparent url('images/trong-dong-white.png') no-repeat 50%;
    background-size: 100% auto;
    opacity: .3;
    filter: drop-shadow(0 0 15px rgba(255,255,255,0.3));
    animation: rotateDrum 20s infinite linear;
}

@keyframes rotateDrum {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Light beam effect */
.light-beam {
    position: absolute;
    width: 100%;
    height: 200%;
    background: linear-gradient(to right, 
        transparent 0%,
        rgba(255,255,255,0.1) 45%,
        rgba(255,255,255,0.15) 50%,
        rgba(255,255,255,0.1) 55%,
        transparent 100%
    );
    transform: rotate(-45deg);
    animation: beam-move 12s infinite linear;
    z-index: 3;
}

@keyframes beam-move {
    0% {
        transform: rotate(-45deg) translateX(-100%);
    }
    100% {
        transform: rotate(-45deg) translateX(100%);
    }
}

.group-event-exam-countdown .slide-wrapper {
    position: relative;
    z-index: 4;
    /*background: linear-gradient(*/
    /*    135deg,*/
    /*    rgba(255, 255, 255, 0.15),*/
    /*    rgba(255, 255, 255, 0.1)*/
    /*);*/
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    /*border-radius: 15px;*/
    /*padding: 20px;*/
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.18);
}


.group-event-exam-countdown div[class^=col-sm-] {
    /* padding-left: 0; */
}

.countdown-left {
    padding-left: 0;
}

.section-category {
    background-color: #fff;
    padding: 0px;
}

.img-cover__wrapper img.sl {
    width: 795px;
    height: 350px;
}
.img-cover__wrapper .logo-stp{
    position: absolute;
    z-index: 2;
    text-align: center;
    width: 100%;
    top:20%;
}

@media (max-width: 768px) {
    .img-cover__wrapper img.sl {
        height: auto;
        width: 100%;
    }
    .countdown-left {
        padding-left: 15px;
    }
  
    .img-cover__wrapper .logo-stp img{
        width: 60px
    }
}
@media (max-width: 480px) {
  .img-cover__wrapper .logo-stp{
        position: relative;
        padding: 0 0 10px 0px;
        background: #d7e8f1;
    }
}    

.product-slider, .category-slider {
    position: relative;
}

.section-category {
    margin-top: 0px;
}

.category-slider__next, .category-slider__prev {
    font-size: 30px;
    cursor: pointer;
    position: absolute;
    z-index: 10;
    top: 40%;
    left: -50px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

.category-slider__next img, .category-slider__prev img {
    box-shadow: 0px 0px 5px #ccc;
    border-radius: 50px;
}

.category-slider__next {
    left: auto;
    right: -50px;
}

.product-slider__next:focus, .product-slider__prev:focus, .category-slider__next:focus, .category-slider__prev:focus {
    outline: 0
}

.category__frame {
    text-align: center;
    color: #333;
}

.category__frame img {
    margin-bottom: 8px;
    /* width: 104px;
    height: 104px; */
    border-radius: 10px;
    margin-top: 8px;
    width: 100%;
    height: 110px;
}

.category__frame a {
    float: left;
}   

.product-slider__next, .product-slider__prev {
    cursor: pointer;
    position: absolute;
    z-index: 10;
    top: -19px;
    right: 45px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
}

.product-slider__next {
    left: auto;
    right: 10px;
}

.products-wrap {
    background-color: #fff;
}

.product-items {
    padding: 15px;
    padding-bottom: 0;
}

/*time */

.c-timeline .title {
    text-align: center;
    text-transform: uppercase;
    /* font-family: "Oswald", sans-serif; */
    padding-top: 20px;
    font-size: 20px;
}

@media (min-width: 992px) {
    .c-timeline .title {
        font-size: 20px;
    }
}

.c-timeline .timeline-inner {
    position: relative;
    /* background: rgba(229, 209, 206, 0.5); */
    padding: 10px 35px 25px 35px;
    overflow-x: scroll;
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
    }
}

.c-timeline .timeline-inner .item {
    position: relative;
    padding-left: 105px;
    margin-bottom: 100px;
    padding-top: 25px;
    opacity: .4;
    min-width: 200px;
}

/* width */

.timeline-content ::-webkit-scrollbar {
    height: 10px!important;
}

/* Track */

.timeline-content ::-webkit-scrollbar-track {
    background: #e3f2f2;
}

/* Handle */

.timeline-content ::-webkit-scrollbar-thumb {
    background: #d7e8e8;
}

/* Handle on hover */

.timeline-content ::-webkit-scrollbar-thumb:hover {
    background: #cbe5e5;
}

.c-timeline .timeline-inner .item.active {
    opacity: 1;
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner .item {
        padding-left: 15px;
        padding-right: 15px;
        padding-top: 0;
        margin-bottom: 0;
        width: 20%;
    }
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner .item:nth-child(1) {
        padding-left: 0px;
    }
}

.c-timeline .timeline-inner .item .image {
    position: absolute;
    left: 0;
    top: 0;
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner .item .image {
        position: initial;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .c-timeline .timeline-inner .item .image {
        left: 10%;
        top: 13px;
    }
    .c-timeline .timeline-inner {
        padding: 20px 35px;
    }
}

.c-timeline .timeline-inner .item .image .img {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    width: 85px;
    height: 85px;
    border-radius: 50%;
}

.svg-inline--fa.fa-w-12 {
    width: .75em;
}

.c-timeline .timeline-inner .item .image .icon {
    position: relative;
    margin-top: 25px;
    text-align: center;
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner .item .image .icon {
        margin-top: 25px;
        width: 100%;
    }
}

.c-timeline .timeline-inner .item .image .icon svg {
    font-size: 34px;
    color: #f6d794;
}

.c-timeline .timeline-inner .item:nth-child(3n+2) .image .icon svg {
    color: #f6affb;
}

.c-timeline .timeline-inner .item:nth-child(3n+2) .image .icon span {
    background: #bc1bce;
}

.c-timeline .timeline-inner .item:nth-child(3n+3) .image .icon svg {
    color: #f9a99f;
}

.c-timeline .timeline-inner .item:nth-child(3n+3) .image .icon span {
    background: #d83621;
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner .item .info {
        margin-top: 20px;
        text-align: center;
    }
}

.c-timeline .timeline-inner .item .info .name {
    margin: 0;
    padding: 0;
    position: relative;
    padding-left: 10px;
    padding-bottom: 5px;
    font-size: 12px;
    text-transform: uppercase;
    border-bottom: 1px solid #000;
    /* font-family: "Oswald",sans-serif; */
    color: #cf9549;
    font-weight: bold;
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner .item .info .name {
        padding-left: 0;
        text-align: center;
        display: inline-block;
        font-size: 14px;
    }
}

.c-timeline .timeline-inner .item:nth-child(3n+1) .info .name {
    color: #cf9549;
}

.c-timeline .timeline-inner .item:nth-child(3n+2) .info .name {
    color: #b34bbf;
}

.c-timeline .timeline-inner .item:nth-child(3n) .info .name {
    color: #d83621;
}

.c-timeline .timeline-inner::before {
    content: '';
    position: absolute;
    height: calc(100% - 0px);
    width: 1px;
    border-left: 1px dashed #000;
    top: 25px;
    left: 140px;
}

@media (min-width: 992px) {
    .c-timeline .timeline-inner::before {
        width: calc(100% + 2240px);
        height: 1px;
        border-left: 0;
        border-bottom: 2px solid #ddd;
        top: 48px;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
    }
}

.c-timeline .timeline-inner .item .image .icon span {
    position: absolute;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    top: 5px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #ffac3e;
}

.timeline-content {
    background: #ecf9f9;
    margin-top: 20px;
}

.c-timeline .timeline-inner span {
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    top: 41px;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    background: #ddd;
}

.date {
    font-size: 13px;
    font-weight: 500;
    padding-top: 7px;
}

.c-timeline .timeline-inner .start-point {
    left: 39px;
}

.c-timeline .timeline-inner .end-point {
    right: -1085px;
}

@media (max-width: 768px) {
    .c-timeline .timeline-inner .start-point {
        left: 140px;
        top: 10px;
    }
    .c-timeline .timeline-inner .end-point {
        left: 140px;
        top: 100%;
    }
}

.right-action.pull-right {
    padding-top: 9px;
    font-size: 12px;
    font-weight: normal;
}

/*----------------------------------*/

/* thông báo của ban tổ chức */

.notifications-index .notification-item {
    position: relative;
    background: #fff;
    overflow: hidden;
    text-decoration: none;
    border: 1px solid #eee;
}

.headline {
    position: relative;
    padding: 5px 15px;
    background: #f33737;
    text-align: left;
    border-bottom: none!important;
    margin-bottom: 0!important;
    font-family: 'Roboto Condensed', sans-serif;
    font-weight: 700;
    font-size: 22px;
}

.headline .panel-title {
    position: relative;
    margin: 0;
    color: #fff;
    font-weight: 500;
    font-size: 22px;
}

.countdown-clock .mb-15.text-center {
    color: #fff;
    font-size: 17px;
    font-weight: bold;
    padding-bottom: 15px;
}

.minutes {
    --color-bg: #d9534f!important;
}

.countdown-clock .btn-danger {
    background-color: #3e9fef!important;
    border: none!important;
    font-weight: 700;
}
.countdown-clock .btn-danger:hover{
    background-color: #278bde!important
}
.countdown-clock .btn-default {
    color:#d01c27!important;
    border: none!important;
    font-weight: 700;
}


.section-video {
    box-shadow: 0 15px 35px rgba(158, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
}

.notifications-index .notification-list {
    position: relative;
    border: 1px solid #fff;
    border-top: 0;
}

.notifications-index .notification-list .list-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0;
    align-items: center;
    -webkit-align-items: center;
    position: relative;
}

.notifications-index .notification-list .list-item .date {
    width: 20%;
    padding: 5px 15px;
    text-align: center;
    margin-bottom: 0;
    font-weight: 500;
}

.notifications-index .notification-list .list-item .title {
    margin: 0;
    font-weight: 400;
    padding: 10px 15px 10px 30px;
    width: 80%;
    line-height: 23px;
    border-left: 1px solid #ea391c;
    font-size: var(--font-size-base);
    font-weight: 500;
    font-size: 14px;
}

.notifications-index .notification-list .list-item::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ea391c;
    border-radius: 50%;
    left: 20%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.notifications-index .notification-list .list-item .title a {
    color: #3b3b3b;
    text-decoration: none;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.ranking-index .ranking-item .ranking-list {
    margin: 0;
    padding: 0;
    border-top: 0;
}

.ranking-index .ranking-item .ranking-list li {
    position: relative;
    list-style: none;
    padding: 15px 0 15px 55px;
    margin: 0 10px;
    border-bottom: 1px dashed #000;
}

@media (min-width: 768px) {
    .ranking-index .ranking-item .ranking-list li {
        padding: 10px 0 10px 45px;
    }
}

@media (min-width: 992px) {
    .ranking-index .ranking-item .ranking-list li {
        padding: 17px 0 16px 90px;
    }
}

.ranking-index .ranking-item .ranking-list li .icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: absolute;
    top: 15px;
    left: 0;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: #f1f1f1;
}

@media (min-width: 768px) {
    .ranking-index .ranking-item .ranking-list li .icon {
        width: 35px;
        height: 35px;
    }
}

@media (min-width: 992px) {
    .ranking-index .ranking-item .ranking-list li .icon {
        width: 55px;
        height: 55px;
        left: 15px;
    }
}

.ranking-index .ranking-item .ranking-list li .icon img {
    max-width: 85%;
    max-height: 85%;
}

@media (min-width: 768px) {
    .ranking-index .ranking-item .ranking-list li .icon img {
        max-width: 65%;
        max-height: 65%;
    }
}

.ranking-index .ranking-item .ranking-list li .info .name {
    margin: 0;
    font-size: 14px;
}

@media (min-width: 768px) {
    .ranking-index .ranking-item .ranking-list li .info .name {
        font-size: 12px;
    }
}

@media (min-width: 992px) {
    .ranking-index .ranking-item .ranking-list li .info .name {
        font-size: 16px;
    }
}

.ranking-index .ranking-item .ranking-list li:last-of-type {
    border-bottom: 0;
}

.list-item-doc {
    border-bottom: 1px solid #eee;
}

.doc-title {
    margin: 0;
    font-weight: 400;
    padding: 6px 15px 2px 15px;
    /* width: 80%; */
    line-height: 21px;
    /* border-left: 1px solid #ea391c; */
    font-size: var(--font-size-base);
    font-weight: 500;
    font-size: 14px;
}

.list-item-doc:last-of-type {
    border-bottom: 0;
}

/* video trang chur */

.video-item .wrapper {
    padding: 15px 15px 15px 15px;
    border: 1px solid #eee;
    border-top: 0;
    background: #fff;
}

.video-item .list-item {
    margin: 0;
    padding: 0;
    border-top: 0;
}

.video-item .list-item .item {
    list-style: none;
}

.video-item .list-item .item:nth-child(1) {
    padding-top: 0;
    border-top: 0;
}

@media (min-width: 768px) {
    .video-item .list-item .item .inner {
        position: relative;
        padding-left: 200px;
    }
}

@media (min-width: 992px) {
    .video-item .list-item .item .inner {
        padding-left: 120px;
    }
}

@media (min-width: 768px) {
    .video-item .list-item .item:nth-child(1) .inner {
        position: initial;
        padding-left: 0;
    }
}

@media (min-width: 992px) {
    .video-item .list-item .item:nth-child(1) .inner {
        margin: -15px -16px 0 -16px;
    }
}

.video-item .list-item .item:nth-child(2) .img {
    content: '';
    position: absolute;
    width: 50%;
    top: 0;
    left: 0;
    z-index: 2;
    pointer-events: none;
}

.video-item .list-item .item .inner .img, .c-news .list .item .img {
    position: relative;
    overflow: hidden;
     border: 1px solid darkgrey;

}

@media (min-width: 768px) {
    .video-item .list-item .item .inner .img {
        position: absolute;
        top: 0;
        left: 0;
        width: 105px;
        height: 70px;
            border: 1px solid darkgrey;
    }
}

@media (min-width: 768px) {
    .video-item .list-item .item:nth-child(1) .inner .img {
        position: relative;
        width: auto;
        height: auto;
        margin: 15px;
    }
}

.video-item .list-item .item .inner .img a {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    -webkit-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

.video-item .list-item .item .inner .img a>img {
    width: 100%;
    -o-object-fit: contain;
    object-fit: contain;
}

.video-item .list-item .item .inner .img .icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: absolute;
    z-index: 3;
    pointer-events: none;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    width: 55px;
    height: 55px;
    border-radius: 50%;
    border: 1px solid #fff;
    opacity: .7;
}

.video-item .list-item .item .inner .img:after {
    content: '';
    display: block;
    padding-bottom: 56.25%;
}

.video-item .list-item .item:nth-child(1) .inner .img .icon {
    display: none;
}

@media (min-width: 768px) {
    .video-item .list-item .item:nth-child(1) .inner .info {
        min-height: auto;
        margin-top: 20px;
        padding-bottom: 15px;
    }
}

@media (min-width: 992px) {
    .video-item .list-item .item:nth-child(1) .inner .info {
        margin-top: 15px;
        padding-bottom: 5px;
        padding-left: 15px;
        padding-right: 15px;
    }
}

@media (min-width: 768px) {
    .video-item .list-item .item .inner .info {
        min-height: 70px;
    }
}

@media (min-width: 992px) {
    .video-item .list-item .item .inner .info .title {
        line-height: 1.3;
        max-height: 3.9em;
    }
}

.video-item .list-item .item:nth-child(1) .inner .info .title {
    font-weight: 700;
    line-height: 1.5;
}
.info h5{
       line-height: 1.5;
}

@media (min-width: 768px) {
    .video-item .list-item .item:nth-child(1) .inner .info .title {
        font-size: 16px;
    }
}

/*--------------------------------------------------*/

/* footer top*/
.footer-top .title {
    color: #fff;
}

.footer-group h3, .footer-group h5 {
    font-size: 15px;
    font-weight: normal;
    padding-bottom: 10PX;
    margin-top: 0;
    margin-bottom: 0;
}

.list-item-doc .date {
    font-size: 12px;
    color: #999;
    padding: 3px 15px 6px 15px;
    font-size: 14px;
    font-weight: normal;
}

.contact {
    margin-top: 0px !important;
}

.contact img{
    margin-top: 15px;
}

.footer-group {
   /*** background: url('images/ft_07.jpg') repeat-x center bottom #0d549d!important;
    padding: 30px 0 70px 0!important;**/
}

.columns-widget .footer-group {
    background: none!important;
}

/************************/

.navbar-default .navbar-toggle .icon-bar {
    background-color: #ca0000!important;
}

@media (max-width: 1023px) {
    .slide-wrapper .col-sm-12.col-md-8 {
        padding-right: 15px;
    }
    .container-fluil {
        height: 40px;
        display: block;
    }
    .group-event-exam-countdown {
        padding: 30px 0;
    }
    .date {
        padding-left: 20px;
        font-size: 14px;
    }
    .c-timeline .timeline-inner .item .info .name {
        padding-left: 20px;
        font-size: 16px;
    }
    .c-timeline .timeline-inner .item {
        margin-bottom: 0
    }
    .timeline-content {
        margin-top: 10px;
    }
    .section-video {
        margin-top: 20px;
    }
    .notifications-index.ranking-index {
        margin-top: 20px;
    }
    .category-slider__next {
        display: none;
    }
    .video-item .list-item .item:nth-child(2) {
        position: relative;
    }
    .video-item .list-item .item:nth-child(2) .info {
        position: absolute;
        top: 0;
        left: 55%;
    }
}

@media (max-width: 480px) {
    .c-timeline .timeline-inner .item {
        padding-left: 55PX;
    }
    .c-timeline .timeline-inner::before {
        left: 90PX;
    }
    .c-timeline .timeline-inner .item .image {
        left: 5%;
    }
    .c-timeline .timeline-inner .start-point {
        left: 91px;
    }
    .c-timeline .timeline-inner .end-point {
        left: 91px;
    }
}

.breadcrumb {
    margin-top: 20px!important;
    background-color: #f4f4f4;
    */
}

.modul-name.main-header a {
    color: #d10000!important;
}

.modul-name.main-header a {}

.searchVB {
    background: #d10000 none repeat scroll 0 0 !important;
    display: table;
    padding: 1px 2px 2px;
    margin-bottom: 20px;
}

.searchVB .head {
    color: white;
    font-weight: 500;
    padding: 8px 15px;
    text-transform: uppercase;
}

.searchVB .bg {
    width: 100%;
    display: inline-block;
    clear: both;
    background: #fff;
    padding: 20px;
}

.blocks-document-title {
    position: relative;
    padding-bottom: 10px;
    text-transform: uppercase;
    margin-bottom: 15px;
}

.blocks-document-title h3 {
    font-size: 18px;
}

.blocks-document-title h3:before {
    position: absolute;
    top: 95%;
    content: '';
    width: 100%;
    height: 2px;
    background-color: #ddd;
    left: 0;
}

.blocks-document-title h3:after {
    position: absolute;
    top: 95%;
    content: '';
    width: 100px;
    height: 2px;
    background-color: #d10000;
    left: 0;
}

.grid-news .media-heading {
    font-size: 16px;
    margin-bottom: 10px;
}
.info-help div{
    margin-bottom: 5px;
}

.h2-tit-news {
    font-size: 18px;
    margin-bottom: 10px;
}

.detail-news-p {
    margin-bottom: 30px;
}

table.docs-content-detail tr td {
    padding: 10px 0;
}

table.docs-content-detail tr td:nth-child(1) {
    font-weight: bold;
}

.panel-ketqua .panel-title {
    font-size: 24px;
    font-weight: bold;
    color: #d10000;
}

.wrap-content-ketqua .justify-content-center {
    margin: 0 auto;
    padding: 20px 15px;
}

.result-quiz {
    padding: 15px 5px;
}

.result-quiz .result-q-d {
    font-weight: 500;
}

.result-quiz .result-q-d .col-md-6 {
    margin-bottom: 5px;
}

.result-quiz .result-q-d span {
    font-size: 16px;
    color: #3c41d4
}

.table-result {
    margin-top: 20px;
}

.date-test {
    color: #333;
    font-size: 18px!important;
    text-transform: none!important;
    text-align: center;
    margin-bottom: 10px;
    font-weight: 700;
}

.name-thi {
    font-size: 24px;
    text-transform: uppercase;
    color: #d10000;
    font-weight: 700;
    text-align: center;
    margin-bottom: 10px;
}

.index-group-item .list-items {
    -ms-flex-pack: distribute;
    justify-content: space-around;
    margin: 0;
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    /* justify-content: center; */
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background: #fff;
}
.index-group-item .group-items{
    margin-top: 20px;
}
.index-group-item .list-items li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    list-style: none;
}

.index-group-item .list-items li img {
    max-width: 100%;
}
.date-thi{
    text-align: center;
    font-size: 18px;
    text-transform: none;
    padding-bottom: 10px;
    font-style: italic;
}
.name-cuocthi{
     text-align: center;
    font-size:30px!important;
    font-weight: bold;
    text-transform: uppercase;
    color: #ca0000;
    padding-bottom: 10px;
    font-family: 'Roboto Condensed', sans-serif;
    font-weight: 700;
    padding: 10px;
    font-size: 30px;
    line-height: 1.3
}
.dapan-upper{
    text-transform: uppercase;
}
.full-height-container{
    /* height: 70%;
    display: flex;
    flex-direction: column; */
    /* justify-content: center; */
    min-height: 70%;
}



/* ----------------------------------------- */
.item-thisinh{
    width: 240px;
    height: auto;
           margin-top: 20px;
  }
  .week{
      background: #8b2020;
      padding: 6px 10px;
      color: #fff;
      border-radius: 10px 10px 0 0;
      font-weight: bold;
      font-size: 16px;
      font-style: italic;
  }
  .detail-thisinh{
      background: #c72e2e;
      color: #fff;
      border-radius: 0 0 10px 10px;
      margin-top: 0;
  }
  .avatar img{
      width: 45px;
      height: 45px;
      border-radius: 50%;
  }
  .wrap-row-1{
      display: flex;
      justify-content: space-between;
  }
  .wrap-row-1 .item-thisinh{
      height: auto;
      width: 265px; 
       margin-top: 20px;
  }
  .wrap-row-1 .detail-thisinh{
      height: 90px;
  }
  .wrap-row-2{
      display: flex;
      justify-content: center;
  }
  .wrap-row-2 .item-thisinh{
      margin-left: 12px;
      margin-right: 12px;
      height: auto;
      margin-top: 20px;
  }
  @media (min-width:1023.98px){
      .wrap-row-2 .item-thisinh:nth-child(1){
          margin-left: 0;
      }
      .wrap-row-2 .item-thisinh:nth-child(5){
          margin-right: 0;
      }
      .dropdown.user.user-menu{
        height: 50px!important;
    }
  }    
  .detail-thisinh{
      padding: 7px;
      height: 100px;
  }
  .name-ts{
      font-weight: bold;
      font-size: 14px;
  }
  .addr{
      font-size: 13px;
  }
  /**.wrap-row-4{
      display: flex;
      justify-content: space-around;
      height: 150px;
  }**/
  @media (max-width:1024px){
      .wrap-row-1 .item-thisinh{
          width: 220px;
      }
  }
  @media (max-width: 980px){
      .wrap-row-1, .wrap-row-2, .wrap-row-4{
           flex-wrap:wrap;
           justify-content: center;
      }
      .wrap-row-1 .item-thisinh, .wrap-row-2 .item-thisinh, .wrap-row-4 .item-thisinh{
          height: 120px; 
          width: auto;
          max-width: 280px; 
          margin-left: 15px;
          margin-right: 15px;
          height: auto;
           margin-top: 10px;
      }
      .wrap-row-1 .item-thisinh .detail-thisinh, .wrap-row-2 .item-thisinh .detail-thisinh{
          height: 90px;
      }
  }
  @media (max-width: 480px){
      .wrap-row-1 .item-thisinh, .wrap-row-2 .item-thisinh, .wrap-row-4 .item-thisinh{
          width: auto;
          max-width: 420px;
          margin-top:10px;
      }
      .wrap-row-1 .item-thisinh .detail-thisinh, .wrap-row-2 .item-thisinh .detail-thisinh{
          height: 85px;
      }
      .list-item .item:nth-child(2) .info h5{
           margin-top: 0;
     }
  }
  
  .top-box h3{
      font-weight: bold;
      margin-bottom: 8px;
  }
  .top-box {
    padding: 18px 0 5px;
    text-align: center;
    background: #bfdef2;
    text-transform: uppercase;
}
  .tong{
      color: #c72e2e;
      font-size: 18px;
      margin-bottom: 10px;
  }
  @media (max-width: 767px){
    .img-thi img{
        width: 100%;
    }
  }
  .navbar-default .navbar-collapse, .navbar-default .navbar-form{
    background-color: #fff;
  }
  .footer-group h3, .footer-group h5{
    line-height: 1.5
  }
  .footer-group {
    background:#9e0000 right top no-repeat!important;
        padding: 20px 0 40px!important;
}
.btn-primary {
    color: #fff;
        border: 1px solid #2896ff;
    background-color: #2896ff;
}
button.btn-primary:hover, button.btn-primary:focus {
    color: #fff;
    background-color: #2896ff;
    border: 1px solid #2896ff;
    padding: 6px 12px;
}
.btn-danger:active:focus{
background-color: #bf2d29;
    border-color: #c9302c;
}

.btn-danger.focus, .btn-danger:focus{
      background-color: #bf2d29;
    border-color: #c9302c;
}
.btn-danger {
    color: #fff;
    border: 2px solid #ca0000;
    background-color: #ca0000;
}
.btn.active.focus, .btn.active:focus, .btn.focus, .btn:active.focus, .btn:active:focus, .btn:focus{
    outline: none;
    outline-offset: none;

}
.btn-primary.focus, .btn-primary:focus {
    color: #fff;
    background-color: #0970b9!important;
    border-color: #0970b9!important;
}
@media (max-width: 480px) {
    .name-cuocthi{
        font-size: 20px!important;
        padding-bottom: 5px;
    }
    .date-thi{
        padding-bottom: 0;
        font-size: 14px;
    }
    .list-item-doc .newsThum{
        padding-right: 0;
        margin-top: 10px;
    }
}
@media (max-width: 768px) {
    .choose{
        margin-right: 10px!important;
        min-height: 58px!important;
    }    
    .choose.a{
            flex-basis: 50px!important;
    }
    .choose-detail input[type=checkbox]+label, 
    .choose-detail input[type=checkbox]:checked+label{
        min-height: 60px!important;
        font-size: 14px!important;
    }
    .swatch-element{
        margin-bottom: 10px!important;
    }
}
@media (max-width: 768px){
    .footer-group {
        font-size: 14px!important;
        padding: 10px 0 50px 0!important;
    }
    .footer-group h3, .footer-group h5{
        padding-bottom: 0!important
    }
}
@media (max-width:1024px){
    .img-cover__wrapper img.sl{
        width: 100%;height: auto;
    }
    .header-kq img{
        width: 100%;
    }
    .header-kq.vnpt img{
        width: auto;
    }
}
.ketqua_wrap{
    background-color: #fff;
    padding: 20px 30px;
        border: 1px solid #c3e4fd;
    margin: 0 auto;
    max-width: 650px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.content-answer-body p span, .content-answer-body p {
    font-size: 16px;
    font-family: Roboto, arial, sans-serif;
}

@media (min-width: 768px){
    .navbar-nav {
        float: left;
        margin: 0;
        padding-left: 180px;
    }
}
.logo{
    position: absolute;
    z-index: 99;
}
.navbar-nav>li{
    z-index: 9999;
}
.questions_content{
    padding: 10px;
    padding-left: 0;
}
.counting-user{
       font-size: 18px;
    color: #fff;
    padding-bottom: 10px;
    text-align: center;
    font-style: italic;
    padding-top: 5px;
}
.h1-sl{
    position: absolute;
    text-align: center;
    bottom: 0;
    text-transform: uppercase;
    text-shadow: 2px 2px 1px white;
    line-height: 130%;
    font-size: 2.5em;
    /**background-color: rgb(0 0 0 / 30%);**/
    width: 100%;
    padding: 10px;
    color: red
}

@media (max-width: 480px){
    .h1-sl{
        text-transform: uppercase;
        text-shadow: 1px 2px 1px white;
        line-height: 3.5rem;
        font-size: 2em;
        color: red
    }
}    
.flip-clock-wrapper{
    margin-bottom: 10px!important;
}
.countdown-clock .headline{
        padding: 10px!important;
}
.countdown-clock .button-group .btn{
    margin-bottom: 15px!important
}
.index-group-item{
    margin-top: 20px;
}
.password-container {display: flex; align-items: center}
.password-container .fa {margin-left: -25px; cursor: pointer}