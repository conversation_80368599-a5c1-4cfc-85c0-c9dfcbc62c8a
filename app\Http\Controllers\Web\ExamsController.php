<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Manage\ExamController;
use App\Models\Exam;
use App\Models\Answer;
use App\Models\ExamConfig;
use App\Models\ExamSessionLog;
use App\Models\Question;
use App\Models\ResultMark;
use App\Models\Subject;
use App\Utils\UploadFileUtil;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\ExamResult;
use App\Models\ExamResultDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use DateTime;
use Carbon\Carbon;
use App\Models\ExamResultFinal;

class ExamsController extends Controller
{
    // Add these constants at the class level
    const ANSWER_PATTERN_KEY = 'exam_answer_pattern_';
    const FOCUS_LOSS_KEY = 'exam_focus_loss_';
    const MOUSE_MOVEMENT_KEY = 'exam_mouse_movement_';
    const SUSPICIOUS_ACTIVITY_THRESHOLD = 3;

    public function index(Request $request)
    {
        if (!$this->checkInfoUser()) {
            return view('web.exams.info');
        }
        if ($this->checkExamActive() <= 0) {
            return view('web.exams.404', ['message' => 'Thời gian của cuộc thi đã hết hoặc chưa khởi tạo']);
        }

        $userId = getUserId();
        $activeExamId = $this->getExamActive();
        $maxOfSession = getExamConfig($activeExamId, 'max_exam_times');
        if ($activeExamId > 0) {
            $maxTimeQuiz = getExamConfig($activeExamId, 'max_time_quiz');
            $countSession = ExamResult::where("user_id", $userId)->where("exam_id", $activeExamId)
                ->whereRaw("(status=1 or time_end is not null
                    or TIMESTAMPDIFF(MINUTE,time_start, NOW())>=$maxTimeQuiz)")
                ->count();
            if ($maxOfSession > 0 && $countSession >= $maxOfSession) {
                return view(
                    'web.exams.404',
                    ['message' => "Bạn đã thi đủ số lần ban tổ chức quy định"]
                );
            }
        }

        if (!$request->session()->has('session_exam')) {
            $request->session()->put('session_exam', $this->generationRandomStr());
        }
        $session_key =  $request->session()->get('session_exam');
        return view('web.exams.index', ['session_key' => $session_key, 'examId' => $activeExamId]);
    }

    public function examsTesting(Request $request)
    {
        if (!$this->checkInfoUser()) {
            return view('web.exams.info');
        }
        if ($this->checkExamActive() <= 0) {
            $request->session()->forget('session_exam');
            //$this->updateStatusExamResult();
            return view('web.exams.404', ['message' => 'Cuộc thi đã hết hạn hoặc chưa khởi tạo']);
        }

        $userId = getUserId();
        $activeExamId = $this->getExamActive();
        $maxOfSession = getExamConfig($activeExamId, 'max_exam_times');
        if ($activeExamId > 0) {
            $maxTimeQuiz = getExamConfig($activeExamId, 'max_time_quiz');
            $countSession = ExamResult::where("user_id", $userId)->where("exam_id", $activeExamId)
                ->whereRaw("(status=1 or time_end is not null
                    or TIMESTAMPDIFF(MINUTE,time_start, NOW())>=$maxTimeQuiz)")
                ->count();
            if ($maxOfSession > 0 && $countSession >= $maxOfSession) {
                return view(
                    'web.exams.404',
                    ['message' => "Bạn đã thi đủ số lần ban tổ chức quy định"]
                );
            }
        }

        if (!$request->session()->has('session_exam') || $request->session()->get('session_exam') == '') {
            return view('web.exams.404', ['message' => 'Bài thi đã kết thúc']);
        }
        if ($request->session()->get('session_exam') != $request->q_id) {
            return view('web.exams.404', ['message' => 'Bài thi đã kết thúc']);
        }
        $rtnResults = [];
        $current_time = $this->getTimeCurrent();

        if ($this->checkStatusExam() <= 0) {
            $is_random = getExamConfig($activeExamId, 'is_random_question');
            $isRandomAnswer = getExamConfig($activeExamId, 'is_random_answer');

            $subjects = Subject::join("ex_exam_subjects", "ex_exam_subjects.subject_id", "=", "ex_subjects.id")
                ->where("ex_subjects.active", 1)
                ->whereNull("ex_exam_subjects.deleted_at")
                ->where("ex_exam_subjects.exam_id", $activeExamId)
                ->get([
                    "ex_exam_subjects.subject_id",
                    "ex_exam_subjects.number_of_multiple_choice_questions",
                    "ex_exam_subjects.number_of_literature_questions",
                    "ex_exam_subjects.number_of_fill_blank_questions" // Thêm trường này
                ]);

            if (count($subjects) == 0) {
                return view('web.exams.404', ['message' => 'Chưa khởi tạo môn thi']);
            }

            $questions = [];
            foreach ($subjects as $key => $sub) {
                // Xử lý câu hỏi trắc nghiệm
                $multipleChoiceQuestions = $isRandomAnswer == 0 ?
                    Question::with('answers') :
                    Question::with(['answers' => function ($query) {
                        $query->inRandomOrder();
                    }]);
                $multipleChoiceQuestions = $multipleChoiceQuestions
                    ->where('exam_id', $activeExamId)
                    ->where('subject_id', $sub->subject_id)
                    ->where('type_id', 0);

                // Xử lý câu hỏi tự luận
                $literatureQuestions = Question::where('exam_id', $activeExamId)
                    ->where('subject_id', $sub->subject_id)
                    ->where('type_id', 1);

                // Thêm xử lý câu hỏi điền khuyết
                $fillBlankQuestions = Question::with('answers')
                    ->where('exam_id', $activeExamId)
                    ->where('subject_id', $sub->subject_id)
                    ->where('type_id', 2);

                $limit = $sub->number_of_multiple_choice_questions ?? 0;
                $limitLiterature = $sub->number_of_literature_questions ?? 0;
                $limitFillBlank = $sub->number_of_fill_blank_questions ?? 0; // Thêm limit cho điền khuyết

                if ($is_random == 1) {
                    $multipleChoiceQuestions = $multipleChoiceQuestions->inRandomOrder();
                    $literatureQuestions = $literatureQuestions->inRandomOrder();
                    $fillBlankQuestions = $fillBlankQuestions->inRandomOrder(); // Random câu điền khuyết nếu cần
                }

                $multipleChoiceQuestions = $multipleChoiceQuestions->limit($limit);
                $literatureQuestions = $literatureQuestions->limit($limitLiterature);
                $fillBlankQuestions = $fillBlankQuestions->limit($limitFillBlank);

                // Gộp các loại câu hỏi
                $questions = $key == 0 ? $multipleChoiceQuestions : $questions->union($multipleChoiceQuestions);
                $questions = $questions->union($literatureQuestions);
                $questions = $questions->union($fillBlankQuestions); // Thêm câu điền khuyết vào tập kết quả
            }

            $numberOfQuestions = $questions->count();
            if ($numberOfQuestions <= 0) {
                return view('web.exams.404', ['message' => 'Kỳ thi chưa được khởi tạo câu hỏi']);
            }

            // Sắp xếp theo type_id để nhóm các loại câu hỏi
            $questions = $questions->orderBy("type_id", "asc")->get();

            DB::transaction(function () use (&$current_time, $questions) {
                $examResult = $this->insertExamResult();
                $current_time = $this->getTimeQuiz($examResult->time_start);
                $this->insertExamResultDetail($examResult->id, $questions);
            });
        }
        $result = $this->getResultActive();
        if (!$this->checkTimeQuiz($activeExamId, $this->getTimeCurrent())) {
            $request->session()->forget('session_exam');
            $this->updateStatusExamResult();
            return view('web.exams.404', ['message' => 'Thời gian làm bài thi đã hết']);
        }
        $startTime = $this->getTimeQuiz($this->getStartTimeQuiz());
        $rtnResults = ExamResultDetail::with(['question', 'answer'])
            ->where('result_id', $this->checkStatusExam())->orderBy('id')->get();
        $rtnResults = $rtnResults->groupBy('question_id');
        $exam = Exam::where('status', 1)->orderBy('id')->first(['id', 'name', 'date_start', 'date_end']);
        $extraQuestion = getExamConfig($activeExamId, 'extra_question');
        return view('web.exams.testing', [
            'rtnResults' => $rtnResults,
            'startTime' => $startTime,
            'result_id' => $result->id,
            'session_key' =>  $request->session()->get('session_exam'),
            'time_now' => $current_time,
            'time_exam_end' => $this->getTimeQuiz($this->getTimeEndExam()),
            'exam' => $exam,
            'extraQuestion' => $extraQuestion
        ]);
    }

    public function getFillBlankAnswers(Request $request)
    {
        $resultId = $this->checkStatusExam();
        $answers = DB::table('ex_result_details')
            ->join('ex_questions', 'ex_result_details.question_id', '=', 'ex_questions.id')
            ->where('ex_result_details.result_id', $resultId)
            ->where('ex_questions.type_id', 2) // Giả sử type_id 2 là câu điền khuyết
            ->whereNull("deleted_at")
            ->select('ex_result_details.question_id', 'ex_questions.content', 'ex_result_details.answer_content')
            ->get();

        return response()->json([
            'success' => true,
            'answers' => $answers
        ]);
    }

    public function insertAnswer(Request $request)
    {
        // Check if exam is still active
        if ($this->checkExamActive() <= 0) {
            $request->session()->forget('session_exam');
            return response()->json(['message' => 'exam_end']);
        }

        $curent_time = $this->getTimeCurrent();
        $examId = $this->getExamActive();
        $userId = getUserId();

        // Check for suspicious activity patterns
//        $isSuspicious = $this->trackSuspiciousActivity($request, $userId, $examId);
        $isSuspicious = false;
        if ($isSuspicious) {
            $captcha = $this->generateCaptcha();
            return response()->json([
                'message' => 'captcha',
                'captchaToken' => $captcha['token'],
                'captchaQuestion' => $captcha['question']
            ]);
        }

        // Check if exam time is still valid
        $flag = $this->checkTimeQuiz($examId, $curent_time);
        if (!$flag) {
            $request->session()->forget('session_exam');
            $this->updateStatusExamResult();
            return response()->json(['message' => 'timeout']);
        }

        // Get the minimum time required between answers
        $min_milliseconds_per_answer = getExamConfig($examId, 'min_milliseconds_per_answer');
        $resultId = $request->has('result_id') ? $request->result_id : 0;

        // If this is a specific fill-in-the-blank submission, get the result ID from the request
        if ($request->has('question_id') && !$resultId) {
            $resultId = $request->result_id ?? 0;
        }

        // Check time between answers for all question types regardless of type
        $now = Carbon::now()->format("Y-m-d H:i:s.u");
        $nowTime = new Carbon($now);

        // Find the most recent answer timestamp for this exam session
        $max = ExamResultDetail::where("result_id", $resultId)
            ->where("is_choose", 1)
            ->max("updated_at");

        if (!empty($max)) {
            $maxTime = new Carbon($max);
            $time_difference_in_milliseconds = $nowTime->diffInMilliseconds($maxTime);

            if ($time_difference_in_milliseconds < $min_milliseconds_per_answer) {
                $captcha = $this->generateCaptcha();
                return response()->json([
                    'message' => 'captcha',
                    'captchaToken' => $captcha['token'],
                    'captchaQuestion' => $captcha['question']
                ]);
            }
        }

        // Process fill-in-the-blank questions
        if ($request->has('question_id')) {
            $questionId = $request->question_id;
            $answer = $request->answer ?? '';

            // Double-check that we have a valid result ID
            if (!$resultId) {
                return response()->json(['message' => 'error', 'error' => 'Invalid result ID']);
            }

            // Validate and save the answer
            DB::beginTransaction();
            try {
                $resultDetail = ExamResultDetail::where('result_id', $resultId)
                    ->where('question_id', $questionId)
                    ->first();

                if ($resultDetail) {
                    $resultDetail->answer_content = $answer;
                    $resultDetail->is_choose = 1;
                    $resultDetail->updated_at = $now;
                    $resultDetail->save();
                } else {
                    DB::rollBack();
                    return response()->json(['message' => 'error', 'error' => 'Question not found']);
                }

                // Calculate and update the score
                $pointTotal = $this->calPointTotal($resultId, $examId);
                $point = $pointTotal['point'];
                $examResult = ExamResult::find($resultId);

                if ($examResult) {
                    $examResult->mark = $point;
                    $examResult->updated_at = date("Y-m-d H:i:s");
                    $examResult->save();

                    $marks = $pointTotal['marks'] ?? [];
                    foreach ($marks as $k => $v) {
                        ResultMark::updateOrCreate(
                            ['result_id' => $resultId, 'subject_id' => $k],
                            ['mark' => $v]
                        );
                    }
                }

                DB::commit();
                return response()->json(['message' => 'success']);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error saving fill-in-the-blank answer: ' . $e->getMessage());
                return response()->json(['message' => 'error', 'error' => $e->getMessage()]);
            }
        }

        // Process multiple-choice questions
        $resultDetailId = $request->result_id ?? 0;
        if ($resultDetailId > 0) {
            // Get the result detail
            $result_detail = ExamResultDetail::where('id', $resultDetailId)->first();

            if (!$result_detail) {
                return response()->json(['message' => 'error', 'error' => 'Answer option not found']);
            }

            $resultId = $result_detail->result_id ?? 0;
            $is_choose = (int)$request->is_choose;
            $only_choose = $this->getCountResult($resultDetailId);

            // Save the answer
            DB::beginTransaction();
            try {
                $result_detail->is_choose = $is_choose;
                $result_detail->updated_at = $now;
                $result_detail->save();

                // For single-choice questions, uncheck all other options
                $question_id = $result_detail->question->id;
                if ($only_choose == 1 && $is_choose == 1) {
                    ExamResultDetail::where('question_id', $question_id)
                        ->where('result_id', $resultId)
                        ->where('id', '<>', $resultDetailId)
                        ->where('is_choose', 1)
                        ->update(['is_choose' => 0]);
                }

                // Calculate and update the score
                $pointTotal = $this->calPointTotal($resultId, $examId);
                $point = $pointTotal['point'];
                $examResult = ExamResult::find($resultId);

                if ($examResult) {
                    $examResult->mark = $point;
                    $examResult->updated_at = date("Y-m-d H:i:s");
                    $examResult->save();

                    $marks = $pointTotal['marks'] ?? [];
                    foreach ($marks as $k => $v) {
                        ResultMark::updateOrCreate(
                            ['result_id' => $resultId, 'subject_id' => $k],
                            ['mark' => $v]
                        );
                    }
                }

                DB::commit();
                return response()->json(['message' => 'success']);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Error saving multiple-choice answer: ' . $e->getMessage());
                return response()->json(['message' => 'error', 'error' => $e->getMessage()]);
            }
        }

        // If we reach here, the request was invalid
        return response()->json(['message' => 'error', 'error' => 'Invalid request']);
    }

    /**
     * Track suspicious activity patterns that may indicate bot usage
     */
    private function trackSuspiciousActivity(Request $request, $userId, $examId)
    {
        $cacheKey = "suspicious_activity_{$userId}_{$examId}";
        $suspiciousCount = Cache::get($cacheKey, 0);

        // Check if we should trigger CAPTCHA based on timing patterns
        $patternKey = self::ANSWER_PATTERN_KEY . $userId . '_' . $examId;
        $timingPattern = Session::get($patternKey, []);

        // Add current timestamp to the pattern
        $timingPattern[] = microtime(true);

        // Keep only the last 10 timestamps
        if (count($timingPattern) > 10) {
            array_shift($timingPattern);
        }

        Session::put($patternKey, $timingPattern);

        // Check for consistent timing patterns (bot-like behavior)
        if (count($timingPattern) >= 5) {
            $intervals = [];
            for ($i = 1; $i < count($timingPattern); $i++) {
                $intervals[] = $timingPattern[$i] - $timingPattern[$i-1];
            }

            // Calculate standard deviation of intervals
            $mean = array_sum($intervals) / count($intervals);
            $variance = 0;

            foreach ($intervals as $interval) {
                $variance += pow(($interval - $mean), 2);
            }

            $stdDev = sqrt($variance / count($intervals));

            // If intervals are too consistent (low standard deviation), this might be a bot
            if ($stdDev < 0.5 && $mean < 5) {  // Suspiciously consistent timing
                $suspiciousCount++;
                Cache::put($cacheKey, $suspiciousCount, 3600); // Store for 1 hour
            }
        }

        // Check focus loss data if available
        $focusLossKey = self::FOCUS_LOSS_KEY . $userId . '_' . $examId;
        $focusLossCount = $request->header('X-Focus-Loss-Count', 0);
        $focusLossTime = $request->header('X-Focus-Loss-Time', 0);

        // Real users typically switch tabs/windows occasionally
        // Bot behavior: Either never losing focus or losing focus in a specific pattern
        if ($focusLossCount == 0 && count($timingPattern) > 5) {
            // No focus loss at all is suspicious for a human
            $suspiciousCount++;
        }

        // Check mouse movement data
        $mouseMovementKey = self::MOUSE_MOVEMENT_KEY . $userId . '_' . $examId;
        $mouseMovementData = $request->header('X-Mouse-Data', '');

        if (!empty($mouseMovementData)) {
            // Store the latest mouse movement data
            Session::put($mouseMovementKey, $mouseMovementData);

            // Analyze mouse movement for bot-like patterns
            $movements = json_decode($mouseMovementData, true);
            if ($movements && isset($movements['linearRatio']) && $movements['linearRatio'] > 0.9) {
                // Too linear movement is suspicious
                $suspiciousCount++;
            }
        } else if (count($timingPattern) > 3) {
            // No mouse movement data after several answers is suspicious
            $suspiciousCount++;
        }

        Cache::put($cacheKey, $suspiciousCount, 3600); // Store for 1 hour

        return $suspiciousCount >= self::SUSPICIOUS_ACTIVITY_THRESHOLD;
    }

    /**
     * Generate a CAPTCHA challenge
     */
    private function generateCaptcha()
    {
        $operations = ['+', '-', '*'];
        $operation = $operations[array_rand($operations)];

        // Generate simple math problem
        $num1 = mt_rand(1, 20);
        $num2 = mt_rand(1, 10);

        if ($operation == '-') {
            // Ensure the result is positive
            if ($num1 < $num2) {
                list($num1, $num2) = [$num2, $num1];
            }
        }

        // Calculate the result
        switch ($operation) {
            case '+': $result = $num1 + $num2; break;
            case '-': $result = $num1 - $num2; break;
            case '*': $result = $num1 * $num2; break;
            default: $result = $num1 + $num2;
        }

        // Store the result in session with a unique token
        $token = Str::random(32);
        Session::put('captcha_' . $token, [
            'result' => $result,
            'expires' => time() + 300 // Valid for 5 minutes
        ]);

        return [
            'token' => $token,
            'question' => "Bao nhiêu là {$num1} {$operation} {$num2}?",
        ];
    }

    public function verifyCaptcha(Request $request)
    {
        $token = $request->input('token');
        $answer = (int) $request->input('answer');

        if (!$token) {
            return response()->json(['success' => false, 'message' => 'Token không hợp lệ']);
        }

        $captchaData = Session::get('captcha_' . $token);

        if (!$captchaData) {
            return response()->json(['success' => false, 'message' => 'CAPTCHA đã hết hạn hoặc không tồn tại']);
        }

        if (time() > $captchaData['expires']) {
            Session::forget('captcha_' . $token);
            return response()->json(['success' => false, 'message' => 'CAPTCHA đã hết hạn']);
        }

        $isCorrect = ($answer === $captchaData['result']);

        if ($isCorrect) {
            // Remove the used captcha only when answer is correct
            Session::forget('captcha_' . $token);

            // Reset suspicious activity counter on successful CAPTCHA
            $userId = getUserId();
            $examId = $this->getExamActive();
            $cacheKey = "suspicious_activity_{$userId}_{$examId}";
            Cache::put($cacheKey, 0, 3600);

            return response()->json(['success' => true, 'message' => 'Xác thực thành công']);
        } else {
            // Increment suspicious count on failed CAPTCHA
            $userId = getUserId();
            $examId = $this->getExamActive();
            $cacheKey = "suspicious_activity_{$userId}_{$examId}";
            $suspiciousCount = Cache::get($cacheKey, 0);
            Cache::put($cacheKey, $suspiciousCount + 1, 3600);

            return response()->json(['success' => false, 'message' => 'Đáp án không chính xác']);
        }
    }

    public function insertExamResult()
    {
        $examResult = new ExamResult();
        $examResult->user_id = getUserId();
        $examId = $this->getExamActive();
        $examResult->exam_id = $examId;
        $examResult->status = 0;
        $examResult->time_start = now();
        $examResult->mark = 0;

        DB::beginTransaction();
        $examResult->save();

        $examCtrl = new ExamController();
        $subjects = $examCtrl->getSubjects($examId);
        $resultMarks = [];
        foreach ($subjects as $sub) {
            $resultMarks[] = ["result_id" => $examResult->id, "subject_id" => $sub->id];
        }
        ResultMark::insert($resultMarks);
        DB::commit();
        return $examResult;
    }

    public function insertExamResultDetail($id, $questions)
    {
        if (isset($questions)) {
            $examId = $questions[0]->exam_id ?? 0;
            $max_number_of_answers = getExamConfig($examId, 'max_number_of_answers');
            DB::beginTransaction();

            foreach ($questions as $q) {
                if ($q->type_id == 0) {
                    // Xử lý câu hỏi trắc nghiệm
                    foreach ($q->answers as $key => $a) {
                        if ($key < $max_number_of_answers) {
                            $exRltDetail = new ExamResultDetail();
                            $exRltDetail->result_id = $id;
                            $exRltDetail->question_id = $q->id;
                            $exRltDetail->answer_id = $a->id;
                            $exRltDetail->is_choose = 0;
                            $exRltDetail->no = $key + 1;
                            $exRltDetail->answer_content = $a->content ?? "";
                            $exRltDetail->save();
                        }
                    }
                }
                elseif ($q->type_id == 2) {
                    // Xử lý câu hỏi điền khuyết - tạo một record duy nhất
                    $exRltDetail = new ExamResultDetail();
                    $exRltDetail->result_id = $id;
                    $exRltDetail->question_id = $q->id;
                    $exRltDetail->is_choose = 0;
                    $exRltDetail->answer_content = ""; // Khởi tạo rỗng cho câu trả lời
                    $exRltDetail->save();
                }
                else {
                    // Xử lý câu hỏi tự luận
                    $exRltDetail_Literature = new ExamResultDetail();
                    $exRltDetail_Literature->result_id = $id;
                    $exRltDetail_Literature->question_id = $q->id;
                    $exRltDetail_Literature->save();
                }
            }
            DB::commit();
        }
    }

    // Lấy thời gian kết thúc cuộc thi
    public function getTimeEndExam()
    {
        $exam = Exam::where('status', 1)->where('date_start', '<=', now())
            ->where('date_end', '>=', now())
            ->orderBy('date_start')->first();
        if ($exam) {
            return $exam->date_end;
        }
        return null;
    }

    public function getExamActive()
    {
        $examActive = Exam::where('status', 1)->orderBy('id')->first(['id']);
        return $examActive->id ?? 0;
    }

    public function getExam()
    {
        $examActive = Exam::where('status', 1)->orderBy('id')->first();
        return $examActive;
    }

    // kiểm tra đã hết thời gian của cuộc thi
    public function checkExamActive()
    {
        $examActive = Exam::where('status', 1)->where('date_start', '<=', now())
            ->where('date_end', '>=', now())
            ->orderBy('date_start')->first();
        if ($examActive) {
            return $examActive->id;
        } else {
            return 0;
        }
    }

    // Lấy kết quả cuộc thi hiện tại
    public function checkStatusExam()
    {
        $examId = $this->getExamActive();
        $examTesting = ExamResult::where('user_id', getUserId())->where('exam_id', $examId)
            ->where('status', 0)->first();
        if ($examTesting) {
            return $examTesting->id;
        } else {
            return 0;
        }
    }

    public function getResultActive()
    {
        $result = ExamResult::where('user_id', getUserId())->where('exam_id', $this->getExamActive())
            ->where('status', 0)->first();
        return $result;
    }

    public function checkQuizIsEnd()
    {
        $examId = $this->getExamActive();
        $examQuiz = ExamResult::where('user_id', getUserId())->where('exam_id', $examId)
            ->where('status', 0)->get();
        if (count($examQuiz) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public function getQuestionAnswers()
    {
        $result_id = $this->checkStatusExam();
        $details = ExamResultDetail::with('question')->where('result_id', $result_id);
        return $details;
    }

    public function getStartTimeQuiz()
    {
        $examId = $this->getExamActive();
        $examTesting = ExamResult::where('user_id', getUserId())
            ->where('exam_id', $examId)
            ->where('status', 0)->first();
        if ($examTesting) {
            return $examTesting->time_start;
        } else {
            return null;
        }
    }

    // kết thúc bài thi
    public function examsTestingEnd($result_id, $session_key, Request $request)
    {
        if ($request->session()->has('session_exam') &&
            $request->session()->get('session_exam') == $session_key
        ) {
            $request->session()->forget('session_exam');
        }

        $examId = $this->getExamActive();
        $max_time_quiz = getExamConfig($examId, 'max_time_quiz');
        $examResult = ExamResult::where('user_id', getUserId())->where('exam_id', $examId)
            ->where('id', $result_id)->first();
        if (isset($examResult)) {
            if ($examResult->status == 0) {
                $examResult->status = 1;
                $current = $this->getTimeCurrent();
                $time_start = $this->getTimeQuiz($examResult->time_start);
                $time_test = $max_time_quiz * 60;
                $minSeconds = getExamConfig($examId, "min_seconds");
                $passedSeconds = $current - $time_start;
                if ($passedSeconds < $minSeconds) {
                    exit; // thời gian làm bài phải đạt thời gian tối thiểu mới cho nộp bài
                }

                if ($passedSeconds > $time_test) {
                    $examResult->time_end = $this->secondsToTime($time_test + $time_start);
                } else {
                    $examResult->time_end = now();
                }
                $examResult->save();
            }
        } else {
            return view('web.exams.404', ['message' => 'Không tồn tại bài thi này']);
        }
        $time_end = $examResult->time_end;
        $time_start = $examResult->time_start;
        $examPointTotal = $this->calPointTotal($examResult->id, $examId);
        $point = $examPointTotal['point'];
        $examResult->mark = $point;
        $examResult->save();
        $rtnResults = $examPointTotal['totals'];
        $distance = ($this->getTimeQuiz($time_end) - $this->getTimeQuiz($time_start)) * 1000;
        $time_test = $max_time_quiz * 60 * 1000;
        if ($time_test < $distance) {
            $distance = $time_test;
        }
        return view('web.exams.end', [
            'rtnResults' => $rtnResults,
            'point' => $point,
            'time_end' => $time_end,
            'time_start' => $time_start,
            'result_id' => $examResult->id,
            'examId' => $examId,
            'guess_number' => $examResult->guess_number,
            'distance' => $distance,
        ]);
    }

    public function secondsToTime($seconds)
    {
        $date = new DateTime();
        $date->setTimestamp($seconds);
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * So sánh hai chuỗi tiếng Việt, xử lý vấn đề khác nhau về vị trí dấu
     *
     * @param string $str1 Chuỗi thứ nhất
     * @param string $str2 Chuỗi thứ hai
     * @return bool Kết quả so sánh
     */
    private function compareVietnameseStrings($str1, $str2)
    {
        // Chuẩn hóa khoảng trắng
        $str1 = trim(preg_replace('/\s+/', ' ', $str1 ?? ''));
        $str2 = trim(preg_replace('/\s+/', ' ', $str2 ?? ''));

        // Chuyển đổi thành chữ thường
        $str1 = mb_strtolower($str1, 'UTF-8');
        $str2 = mb_strtolower($str2, 'UTF-8');

        // Danh sách các cặp chuyển đổi từ kiểu mới sang kiểu cũ
        $replacements = [
            'oá' => 'óa', 'oà' => 'òa', 'oả' => 'ỏa', 'oã' => 'õa', 'oạ' => 'ọa',
            'uý' => 'úy', 'uỳ' => 'ùy', 'uỷ' => 'ủy', 'uỹ' => 'ũy', 'uỵ' => 'ụy',
            'iê' => 'iê', 'yê' => 'yê', 'uô' => 'uô', 'ươ' => 'ươ',
            'hoá' => 'hóa', 'khoá' => 'khóa', 'toá' => 'tóa', 'xoá' => 'xóa', 'loá' => 'lóa',
            'boá' => 'bóa', 'goá' => 'góa', 'doá' => 'dóa', 'moá' => 'móa', 'noá' => 'nóa',
            'poá' => 'póa', 'soá' => 'sóa', 'voá' => 'vóa', 'roá' => 'róa', 'quoá' => 'quóa'
        ];

        // Áp dụng chuyển đổi cho cả hai chuỗi
        foreach ($replacements as $search => $replace) {
            $str1 = str_replace($search, $replace, $str1);
            $str2 = str_replace($search, $replace, $str2);
        }

        // So sánh chuỗi đã chuẩn hóa
        return $str1 === $str2;
    }

    public function calPoints($result_id, $exam_id)
    {
        $data = DB::select(
            "select * from (
            select d.question_id,
            group_concat(if(d.is_choose=1, d.is_choose, null)) as is_choose,
            group_concat(if(d.is_choose=1, d.answer_id, null) order by d.answer_id) as id_cautraloi,
            group_concat(if(d.is_choose=1, d.no, null)) as thu_tu,
            min(d.id) as no_question,
            group_concat(if(d.is_choose=1, d.answer_content,null)
              order by d.answer_id separator '; ') as answer_content
            from ex_result_details d
            where d.result_id = :result_id group by d.question_id
            order by no_question) q
        join (select group_concat(a.is_true) as dap_an,
            group_concat(a.id order by a.id) as id_dapan_dung,
            q.id as q_id, q.type_id, q.content as question_content, q.subject_id,
            group_concat(a.content order by a.id separator '; ') as true_answer_content
            from ex_questions q
            left join ex_answers a on q.id = a.question_id and (a.is_true=1 or q.type_id=1 or q.type_id=2)
            and a.deleted_at is NULL
            where q.exam_id = :exam
            group by q.id, q.type_id, q.content, q.subject_id
        ) a on q.question_id = a.q_id
        order by no_question",
            [
                'result_id' => $result_id,
                'exam' => $exam_id,
            ]
        );
        return $data;
    }

    public function calPointTotal($result_id, $exam_id)
    {
        $point = 0;
        $totals = [];
        $rtn = [];
        $marks = [];
        $data = $this->calPoints($result_id, $exam_id);
        $alphas = range('a', 'z');
        foreach ($data as $key => $result) {
            $totals[$key]['stt'] = $key + 1;
            $questionTypeId = $result->type_id ?? 0;
            $totals[$key]['type_id'] = $questionTypeId;
            $totals[$key]['question_content'] = $result->question_content;
            $totals[$key]['answer_content'] = $result->answer_content ?? "";
            $totals[$key]['true_answer_content'] = $result->true_answer_content ?? "";
            $totals[$key]['id_cautraloi'] = $result->id_cautraloi ?? "";

            if ($result->thu_tu > 0) {
                $totals[$key]['sChooose'] = $alphas[(int)$result->thu_tu - 1];
            }

            $subjectId = $result->subject_id ?? 0;
            if ($subjectId > 0) {
                $marks[$subjectId] = $marks[$subjectId] ?? 0;
            }

            // Mặc định status = 0
            $totals[$key]['status'] = 0;

            // Xử lý câu hỏi trắc nghiệm (type 0)
            if ($questionTypeId == 0) {
                if ($result->is_choose == $result->dap_an && $result->id_cautraloi == $result->id_dapan_dung) {
                    $totals[$key]['status'] = 1;
                    $point = $point + 1;
                    if ($subjectId > 0) {
                        $marks[$subjectId] += 1;
                    }
                }
            }
            // Xử lý câu hỏi điền khuyết (type 2)
            elseif ($questionTypeId == 2) {
                // So sánh câu trả lời với đáp án đúng
                if ($this->compareVietnameseStrings($result->answer_content, $result->true_answer_content)) {
                    $totals[$key]['status'] = 1;
                    $point = $point + 1;
                    if ($subjectId > 0) {
                        $marks[$subjectId] += 1;
                    }
                }
            }
        }

        $rtn['point'] = $point;
        $rtn['totals'] = $totals;
        $rtn['marks'] = $marks;
        return $rtn;
    }

    public function getCountResult($result_detail_id)
    {
        $result_id = $this->checkStatusExam();
        $result = ExamResultDetail::where('id', $result_detail_id)
            ->where('result_id', $result_id)
            ->first();

        if (isset($result)) {
            // Cả câu trắc nghiệm một đáp án và điền khuyết đều trả về 1
            if ($result->question->type_id == 0 || $result->question->type_id == 2) {
                $answers = Answer::where('question_id', $result->question->id)
                    ->where('is_true', 1)->whereNull('deleted_at')->count();
                if ($answers == 1) {
                    return 1;
                }
            }
        }
        return 0;
    }

    public function checkTimeQuiz($examId, $time_now)
    {
        $time_start = $this->getStartTimeQuiz();
        $time_stamp = $this->getTimeQuiz($time_start);
        $time_test = getExamConfig($examId, 'max_time_quiz') * 60;
        $distance = $time_now - $time_stamp;
        if ($distance > $time_test) {
            return false;
        }
        return true;
    }
    public function getTimeQuiz($time)
    {
        $time_start = date_parse_from_format('Y-m-d H:i:s', $time);
        $time_stamp = mktime(
            $time_start['hour'],
            $time_start['minute'],
            $time_start['second'],
            $time_start['month'],
            $time_start['day'],
            $time_start['year']
        );
        return $time_stamp;
    }
    public function getTimeCurrent()
    {
        date_default_timezone_set('Asia/Ho_Chi_Minh');
        $now = time();
        return $now;
    }

    // kết thúc những bài thi thí sinh đang thi dở thì không thi nữa
    public function updateStatusExamResult()
    {
        $examId = $this->getExamActive();
        $results = ExamResult::where('user_id', getUserId())->where('exam_id', $examId)
            ->where('status', 0)->get();
        foreach ($results as $examResult) {
            $examResult->status = 1;
            $timeStart = new DateTime($examResult->time_start);
            $maxTimeQuiz = getExamConfig($examId, 'max_time_quiz');
            $timeEnd = $timeStart->modify("+$maxTimeQuiz minutes");
            $examResult->time_end = $timeEnd;
            $examResult->save();
        }
    }

    public function generationRandomStr()
    {
        return substr(md5(rand()), 0, 30);
    }
    public function getResult()
    {
        $examId = $this->getExamActive();
        $examTesting = ExamResult::where('user_id', getUserId())->where('exam_id', $examId)
            ->where('status', 1)->orderBy('id', 'desc')->first();
        if ($examTesting) {
            return $examTesting->id;
        } else {
            return 0;
        }
    }

    public function finishTesting(Request $request)
    {
        $examId = $this->getExamActive();
        $examQuiz = ExamResult::where('user_id', getUserId())->where('exam_id', $examId)
            ->where('status', 0)->orderBy('id', 'desc')->first();
        $examQuizId = $examQuiz->id ?? 0;
        if ($examQuizId > 0) {
            $time_start = $this->getTimeQuiz($examQuiz->time_start);
            $current = $this->getTimeCurrent();
            $minSeconds = getExamConfig($examId, "min_seconds");
            $passedSeconds = $current - $time_start;
            if ($passedSeconds < $minSeconds) {
                //return ['success' => false, 'msg' => 'Chưa đủ thời gian tối thiểu để nộp bài'];
                exit; // thời gian làm bài phải đạt thời gian tối thiểu mới cho nộp bài
            }

            $exRltDetails = ExamResultDetail::join(
                "ex_questions",
                "ex_questions.id",
                "=",
                "ex_result_details.question_id"
            )
                ->where("ex_result_details.result_id", $examQuizId)
                ->where("ex_questions.type_id", 1)->whereNull("ex_questions.deleted_at")
                ->get(["ex_result_details.id", "ex_questions.id as question_id"]);

            foreach ($exRltDetails as $exRltDetail) {
                $questionId = $exRltDetail->question_id ?? 0;
                $key = "literature_answer$questionId";
                $literatureAnswer = $request->$key ?? '';
                $maxWordsOfAnswer = getExamConfig($examId, 'max_words_of_answer');
                if ($maxWordsOfAnswer > 0) {
                    // remove duplicate spaces
                    $literatureAnswer = preg_replace('/\s+/', ' ', trim($literatureAnswer));
                    // check number of words
                    $numberOfWords = substr_count($literatureAnswer, " ");
                    if ($numberOfWords > $maxWordsOfAnswer) {
                        return ['success' => false, 'msg' => "Câu trả lời không được vượt quá $maxWordsOfAnswer từ"];
                    }
                }
                $exRltDetail->answer_content = $literatureAnswer;
                $exRltDetail->is_choose = 1;

                if ($request->hasFile('literature_file'.$questionId)) {
                    $file = UploadFileUtil::uploadFile($request, 'literature_file'.$questionId);
                    $exRltDetail->answer_id = $file->id ?? 0;
                }
                $exRltDetail->save();
            }

            if ($request->has('num_person')) {
                if (is_numeric($request->num_person)) {
                    $num_person = $request->num_person < 1 ? 0 : $request->num_person;
                    $examQuiz->guess_number = $num_person;
                    $examQuiz->save();
                }
            }
        }
        return ['success' => true, 'msg' => 'success'];
    }

    // kết quả thi của mỗi user
    public function quizResults(Request $request)
    {
        $examId = $this->getExamActive();
        $examCfg = ExamConfig::where("exam_id", $examId)->first(["is_view_previous"]);
        $isViewPrevious = $examCfg->is_view_previous ?? 0;
        $quizResults = ExamResult::join("ex_exams", "ex_exams.id", "=", "ex_results.exam_id")
            ->join("ex_exam_config", "ex_exam_config.exam_id", "=", "ex_exams.id")
            ->whereNull("ex_exams.deleted_at")->where("ex_results.user_id", getUserId())
            ->whereRaw("(ex_results.status=1 or ex_results.time_end is not null
                or TIMESTAMPDIFF(MINUTE, ex_results.time_start, NOW())>=ex_exam_config.max_time_quiz)");
        if ($isViewPrevious == 0) {
            $quizResults = $quizResults->where('ex_exams.id', '<>', $examId);
        }
        $quizResults = $quizResults->orderBy("ex_results.time_start", "desc")
            ->selectRaw("ex_results.*, ex_exams.name as examName")->paginate(15);
        return view('web.users.quiz_result', ['quizResults' => $quizResults]);
    }

    // Chi tiết bài thi của thí sinh
    public function quizResultDetail($result_id)
    {
        $orgTypeId = getOrgTypeId();
        $examResult = ExamResult::join("ex_exams", "ex_exams.id", "=", "ex_results.exam_id")
            ->join("users", "users.id", "=", "ex_results.user_id")
            ->join("user_profiles", "user_profiles.user_id", "=", "users.id")
            ->join("user_offices", "user_offices.id", "=", "user_profiles.office_id")
            ->join("ex_exam_config", "ex_exam_config.exam_id", "=", "ex_exams.id")
            ->whereNull("users.deleted_at")
            ->whereNull("ex_exams.deleted_at")->where("ex_results.id", $result_id);

        if ($examResult->count() < 1) {
            return view('web.exams.404', ['message' => 'Bài thi chưa kết thúc hoặc không tồn tại']);
        }

        if ($orgTypeId == 0) {
            $examResult = $examResult->where("ex_results.user_id", getUserId())
                ->whereRaw("(ex_results.status=1 or ex_results.time_end is not null
                    or TIMESTAMPDIFF(MINUTE, ex_results.time_start, NOW())>=ex_exam_config.max_time_quiz)");

            $examId = $this->getExamActive();
            $examCfg = ExamConfig::where("exam_id", $examId)->first(["is_view_previous"]);
            $isViewPrevious = $examCfg->is_view_previous ?? 0;
            if ($isViewPrevious == 0) {
                $examResult = $examResult->where('ex_exams.id', '<>', $examId);
            }
        }

        $examResult = $examResult->selectRaw("ex_results.*, users.name, user_offices.name as office_name,
            if(TIMESTAMPDIFF(MINUTE, ex_results.time_start, NOW())>=ex_exam_config.max_time_quiz, 1, 0) as ket_thuc,
            ex_exams.name as examName")
            ->first();
        if (isset($examResult)) {
            $time_end = $examResult->time_end;
            $time_start = $examResult->time_start;
            $rtnResults = $this->calPointTotal($result_id, $examResult->exam_id)['totals'];
            $marks = ResultMark::join("ex_subjects", "ex_subjects.id", "=", "ex_result_marks.subject_id")
                ->where("result_id", $result_id)
                ->get(["ex_subjects.name", "ex_result_marks.mark", "ex_result_marks.literature_mark"]);

            return view('web.users.quiz_detail', [
                'rtnResults' => $rtnResults,
                'time_end' => $time_end,
                'time_start' => $time_start,
                'examResult' =>  $examResult,
                'marks' => $marks
            ]);
        } else {
            return view('web.exams.404', ['message' => 'Bài thi chưa kết thúc hoặc không tồn tại']);
        }
    }

    // kiểm tra thông tin người dùng vào thi
    public function checkInfoUser()
    {
        $profile = auth()->user()->userProfile;
        /*if (!isset($profile->address)) {
            return false;
        }
        if (!isset($profile->identification)) {
            return false;
        }
        if (!isset($profile->date_of_birth)) {
            return false;
        }
        if (!isset($profile->sex)) {
            return false;
        }
        if (!isset($profile->region_id)) {
            return false;
        }*/
        if (!isset($profile->office_id)) {
            return false;
        }
        return true;
    }
    //danh sách cuộc thi
    public function ketqua(Request $request)
    {
        $quizResults = Exam::where('date_end', '<', now())
            ->where('status', 0)
            ->where('is_public', 1)
            ->orderBy('id', 'desc')->paginate(15);
        return view('web.exams.quiz_result', [
            'quizResults' => $quizResults,
        ]);
    }
    //chi tiết kết quả cuộc thi
    public function chitietKetqua($exam_id)
    {
        $quizResult = Exam::where('date_end', '<', now())
            ->where('id', $exam_id)
            ->where('status', 0)
            ->where('is_public', 1)
            ->first();
        if (!isset($quizResult)) {
            return view('web.exams.404', ['message' => 'Cuộc thi chưa kết thúc hoặc không tồn tại']);
        }

        $data = ExamResultFinal::where('exam_id', $exam_id)
            ->selectRaw("name, office_name, mark, date_format(time_start, '%d/%m/%Y %H:%i:%s') as bat_dau,
                    timestampdiff(second, time_start, time_end) as time_testing")
            ->get();
        $giainhat = $data->first();
        $giainhi = $data->filter(function ($value, $key) {
            if ($key == 1 || $key == 2) {
                return $value;
            }
        });
        $giaiba = $data->filter(function ($value, $key) {
            if ($key >= 3 && $key <= 5) {
                return $value;
            }
        });
        $giaikk = $data->filter(function ($value, $key) {
            if ($key > 5 && $key <= 10) {
                return $value;
            }
        });
        $exam = Exam::where('id', $exam_id)->first();
        $dap_an = $this->getDapanVongthi($exam_id);
        return view('web.exams.quiz_result_detail', [
            'quizResults' => $data,
            'giainhat' => $giainhat,
            'giainhi' => $giainhi,
            'giaiba' => $giaiba,
            'giaikk' => $giaikk,
            'exam' => $exam,
            'dap_an' => $dap_an,
        ]);
    }

    public function getDataKetqua($totalUsers, $exam_id)
    {
        $data = DB::select(
            "SELECT a.*, ABS(a.guess_number - :totalUsers) as guess, u.name," .
                " p.address, r.full_name from (" .
                "select r.id, r.user_id, r.mark, TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) as distance," .
                "r.guess_number,DATE_FORMAT(r.time_start, '%d/%m/%Y %H:%i:%s') as time_start   from ex_results r " .
                "where r.exam_id= :exam_id and r.guess_number is NOT NULL and r.deleted_at is null " .
                "and r.id=( " .
                "select r2.id from ex_results r2  " .
                "where r2.exam_id=r.exam_id and r2.user_id=r.user_id " .
                "order by r2.mark desc, r2.time_end - r2.time_start asc limit 1) " .
                ") a " .
                "LEFT JOIN users u on a.user_id = u.id " .
                "LEFT JOIN user_profiles p on u.id  = p.user_id " .
                "LEFT JOIN regions r on r.id = p.region_id" .
                " ORDER BY a.mark desc, ABS(a.guess_number - :totalUsers) asc, a.distance asc",
            [
                'totalUsers' => $totalUsers,
                'exam_id' => $exam_id,
            ]
        );
        return $data;
    }

    public function getDataKetqua1($totalUsers, $exam_id, $limit)
    {
        $data = DB::select(
            "SELECT a.*, b.name, CONCAT(b.address,' - ' ,b.full_name) as address,
             ABS(a.guess_number - :totalUsers) as guess, $totalUsers as total_user,
             case when month(a.time_start) > 2 then DATE_FORMAT(a.time_start, '%d/%c/%Y %H:%i:%s')
              else DATE_FORMAT(a.time_start, '%d/%m/%Y %H:%i:%s') end as time_start,
             DATE_FORMAT(a.time_end, '%d/%m/%Y %H:%i:%s') as time_end
            from (
            SELECT @row_number:=CASE
             WHEN @customer_no = a.user_id
             THEN @row_number + 1
             ELSE 1
             END AS num,
             @customer_no:= a.user_id as num_user_id, a.* from
             ( select r.id, r.user_id, r.mark,
             TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) as distance,
             r.guess_number,  time_start, time_end, ABS(r.guess_number - :totalUsers) as guess
             from ex_results r
             where r.exam_id= :exam_id and r.guess_number is NOT NULL and r.deleted_at is null
            ORDER BY r.user_id, r.mark desc, ABS(r.guess_number - :totalUsers) asc,
            TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) asc, r.time_start asc
            ) a, (SELECT @customer_no:=0,@row_number:=0) as t
             ) a
            join (
              SELECT rs.user_id, p.address, r.full_name, u.name
                from ex_results rs
                join users u on rs.user_id = u.id
                LEFT JOIN user_profiles p on u.id  = p.user_id
                LEFT JOIN regions r on r.id = p.region_id
                where rs.exam_id= :exam_id
                and rs.deleted_at is null
                group by rs.user_id
            ) b on a.user_id = b.user_id
            WHERE a.num = 1
            ORDER BY a.mark desc, ABS(a.guess_number - :totalUsers) asc, a.distance asc, a.time_start asc
            limit :limit",
            [
                'totalUsers' => $totalUsers,
                'exam_id' => $exam_id,
                'limit' => $limit,
            ]
        );
        return $data;
    }

    public function getDapanVongthi($exam_id)
    {
        $data = DB::select(
            "SELECT t.num_id, GROUP_CONCAT(t.num) as dapan from " .
                "(SELECT @row_number:= CASE " .
                " WHEN @customer_no = a.id  " .
                " THEN @row_number + 1 ELSE 1  END AS num, " .
                " @customer_no := a.id  num_id, a.is_true " .
                "FROM " .
                "(SELECT q.id, q.exam_id, a.content, a.is_true  " .
                "from ex_questions q " .
                "join ex_answers a on q.id = a.question_id  " .
                " where q.exam_id = :exam_id and q.deleted_at is null ) a, " .
                "(SELECT @customer_no:=0,@row_number:=0) as t " .
                " ) t " .
                "WHERE t.is_true =1 " .
                " GROUP BY t.num_id",
            [
                'exam_id' => $exam_id,
            ]
        );
        return $data;
    }

    public function getCuocthiTruoc()
    {
        $exams = Exam::where('date_end', '<', now())
            ->where('status', 0)
            ->orderBy('date_end', 'desc')->first();
        return $exams;
    }

    public function logSessionData(Request $request)
    {
        $userId = getUserId();

        // Lấy exam_id và result_id từ request
        $resultId = $request->input('result_id');
        $examId = $request->input('exam_id') ?? $this->getExamActive();

        // Kiểm tra xem có mảng logs không
        $logs = $request->input('logs', []);

        // Nếu có mảng logs, xử lý từng log entry
        if (!empty($logs) && is_array($logs)) {
            foreach ($logs as $logEntry) {
                $eventType = $logEntry['type'] ?? 'unknown';
                $reason = $logEntry['reason'] ?? '';
                $warnings = $logEntry['warnings'] ?? 0;
                $importance = $logEntry['importance'] ?? 0;

                // Tính toán mức độ đáng ngờ
                $suspiciousLevel = $importance;
                if ($warnings > 0) {
                    $suspiciousLevel = max($suspiciousLevel, min(10, $warnings * 2));
                }

                // Tạo ghi chú từ thông tin log
                $notesArray = [];
                if (!empty($reason)) {
                    $notesArray[] = "Reason: " . $reason;
                }
                if ($warnings > 0) {
                    $notesArray[] = "Warnings: " . $warnings;
                }
                $notes = !empty($notesArray) ? json_encode($notesArray) : json_encode($logEntry);

                // Chuẩn bị dữ liệu để lưu vào database
                $sessionData = [
                    'user_id' => $userId,
                    'exam_id' => $examId,
                    'exam_result_id' => $resultId,
                    'mouse_data' => [], // Đảm bảo là mảng trống
                    'keyboard_data' => [], // Đảm bảo là mảng trống
                    'focus_data' => [], // Đảm bảo là mảng trống
                    'device_info' => $request->input('device_info', ''),
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->header('User-Agent'),
                    'event_type' => $eventType,
                    'notes' => $notes,
                    'suspicious_level' => $suspiciousLevel
                ];

                // Ghi log dữ liệu nhận được để debug
                \Log::debug('Processing log entry: ' . json_encode([
                    'log_entry' => $logEntry,
                    'processed_data' => $sessionData
                ]));

                try {
                    // Lưu dữ liệu vào database
                    $log = ExamSessionLog::create($sessionData);

                    // Kiểm tra nếu đây là sự kiện kết thúc bài thi
                    if ($eventType === 'exam_terminated') {
                        // Ghi log cảnh báo
                        \Log::warning("Exam terminated for user {$userId} in exam {$examId}. Suspicious level: {$suspiciousLevel}, Reason: {$reason}, Warnings: {$warnings}");
                    }
                } catch (\Exception $e) {
                    // Ghi log lỗi nhưng không hiển thị cho client
                    \Log::error('Failed to save log entry: ' . $e->getMessage());
                    \Log::error('Exception details: ' . $e->getTraceAsString());
                }
            }

            // Trả về thành công sau khi xử lý tất cả các log
            return response()->json(['success' => true]);
        } else {
            // Xử lý định dạng cũ (không có mảng logs)
            // Đảm bảo các trường dữ liệu là mảng
            $mouseData = $request->input('mouse_data', []);
            if (!is_array($mouseData)) {
                $mouseData = [];
            }

            $keyboardData = $request->input('keyboard_data', []);
            if (!is_array($keyboardData)) {
                $keyboardData = [];
            }

            $focusData = $request->input('focus_data', []);
            if (!is_array($focusData)) {
                $focusData = [];
            }

            $deviceInfo = $request->input('device_info', '');
            $eventType = $request->input('event_type', 'periodic');
            $notes = $request->input('notes', '');
            $suspiciousLevel = $request->input('suspicious_level', 0);

            // Tính toán mức độ đáng ngờ nếu không được cung cấp
            if (!$suspiciousLevel) {
                // Phân tích loại sự kiện
                if (strpos($eventType, 'security_') === 0) {
                    $suspiciousLevel += 3;

                    // Phân tích loại sự kiện bảo mật cụ thể
                    $securityEventType = str_replace('security_', '', $eventType);
                    switch ($securityEventType) {
                        case 'exam_terminated':
                            $suspiciousLevel += 5;
                            break;
                        case 'incident':
                            $suspiciousLevel += 3;
                            break;
                        default:
                            $suspiciousLevel += 1;
                            break;
                    }
                }
            }

            // Giới hạn mức độ đáng ngờ tối đa là 10
            $suspiciousLevel = min(10, $suspiciousLevel);

            // Chuẩn bị dữ liệu để lưu vào database
            $sessionData = [
                'user_id' => $userId,
                'exam_id' => $examId,
                'exam_result_id' => $resultId,
                'mouse_data' => $mouseData,
                'keyboard_data' => $keyboardData,
                'focus_data' => $focusData,
                'device_info' => $deviceInfo,
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'event_type' => $eventType,
                'notes' => $notes,
                'suspicious_level' => $suspiciousLevel
            ];

            // Ghi log dữ liệu nhận được để debug
            \Log::debug('Session log data received (old format): ' . json_encode([
                'request_data' => $request->all(),
                'processed_data' => $sessionData
            ]));

            try {
                // Lưu dữ liệu vào database
                $log = ExamSessionLog::create($sessionData);

                // Kiểm tra nếu đây là sự kiện kết thúc bài thi
                if (strpos($eventType, 'exam_terminated') !== false) {
                    // Ghi log cảnh báo
                    \Log::warning("Exam terminated for user {$userId} in exam {$examId}. Suspicious level: {$suspiciousLevel}");
                }

                return response()->json(['success' => true]);
            } catch (\Exception $e) {
                // Ghi log lỗi nhưng không hiển thị cho client
                \Log::error('Failed to save session data: ' . $e->getMessage());
                \Log::error('Exception details: ' . $e->getTraceAsString());
                return response()->json(['success' => true]); // Vẫn trả về thành công để tránh cảnh báo
            }
        }
    }

    /**
     * Lấy trạng thái cảnh báo bảo mật hiện tại của người dùng
     */
    public function getWarningState(Request $request)
    {
        try {
            $userId = getUserId();
            $resultId = $request->input('result_id');
            $examId = $request->input('exam_id');
            $deviceFingerprint = $request->input('device_fingerprint');

            if (!$resultId || !$examId) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Đếm số lần vi phạm bảo mật từ logs
            $warningCount = ExamSessionLog::where('user_id', $userId)
                ->where('exam_id', $examId)
                ->where('exam_result_id', $resultId)
                ->where('suspicious_level', '>=', 5) // Chỉ đếm các vi phạm nghiêm trọng
                ->whereIn('event_type', [
                    'security_devtools_detected',
                    'security_auto_click_detected',
                    'security_auto_focus_detected',
                    'security_abnormal_mouse_movement',
                    'security_bot_mouse_movement',
                    'security_abnormal_keyboard_pattern',
                    'security_repeating_key_sequence',
                    'security_abnormal_click_pattern',
                    'security_repeated_position_clicks',
                    'security_synthetic_mouse_event',
                    'security_synthetic_event_dispatch',
                    'security_excessive_page_reloads',
                    'security_suspicious_page_reloads',
                    'security_multiple_tabs_detected'
                ])
                ->count();

            // Ghi log việc khôi phục trạng thái
            ExamSessionLog::create([
                'user_id' => $userId,
                'exam_id' => $examId,
                'exam_result_id' => $resultId,
                'mouse_data' => [],
                'keyboard_data' => [],
                'focus_data' => [],
                'device_info' => ['fingerprint' => $deviceFingerprint],
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'event_type' => 'security_state_restored',
                'notes' => json_encode([
                    'warning_count' => $warningCount,
                    'device_fingerprint' => $deviceFingerprint
                ]),
                'suspicious_level' => 0
            ]);

            return response()->json([
                'success' => true,
                'warning_count' => $warningCount
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get warning state: ' . $e->getMessage());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Lưu trạng thái cảnh báo bảo mật của người dùng
     */
    public function saveWarningState(Request $request)
    {
        try {
            $userId = getUserId();
            $resultId = $request->input('result_id');
            $examId = $request->input('exam_id');
            $warningCount = $request->input('warning_count');
            $deviceFingerprint = $request->input('device_fingerprint');
            $sessionId = $request->input('session_id');

            if (!$resultId || !$examId || !is_numeric($warningCount)) {
                return response()->json(['error' => 'Missing required parameters'], 400);
            }

            // Ghi log việc lưu trạng thái cảnh báo
            ExamSessionLog::create([
                'user_id' => $userId,
                'exam_id' => $examId,
                'exam_result_id' => $resultId,
                'mouse_data' => [],
                'keyboard_data' => [],
                'focus_data' => [],
                'device_info' => [
                    'fingerprint' => $deviceFingerprint,
                    'session_id' => $sessionId
                ],
                'ip_address' => $request->ip(),
                'user_agent' => $request->header('User-Agent'),
                'event_type' => 'security_warning_state_saved',
                'notes' => json_encode([
                    'warning_count' => $warningCount,
                    'device_fingerprint' => $deviceFingerprint,
                    'session_id' => $sessionId
                ]),
                'suspicious_level' => min(10, $warningCount * 2) // Mức độ nghi ngờ tăng theo số cảnh báo
            ]);

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            \Log::error('Failed to save warning state: ' . $e->getMessage());
            return response()->json(['error' => 'Internal server error'], 500);
        }
    }
}
