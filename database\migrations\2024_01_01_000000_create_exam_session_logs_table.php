<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateExamSessionLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('exam_session_logs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('user_id')->unsigned();
            $table->integer('exam_id')->unsigned();
            $table->integer('exam_result_id')->unsigned();
            $table->json('mouse_data')->nullable();
            $table->json('keyboard_data')->nullable();
            $table->json('focus_data')->nullable();
            $table->json('device_info')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('event_type', 100);
            $table->text('notes')->nullable();
            $table->tinyInteger('suspicious_level')->default(0);
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['user_id', 'exam_id', 'exam_result_id']);
            $table->index(['event_type']);
            $table->index(['suspicious_level']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exam_session_logs');
    }
}
