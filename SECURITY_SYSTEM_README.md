# Hệ Thống Bảo Mật Thi Trực Tu<PERSON>ến - <PERSON><PERSON><PERSON> Bản Cải Tiến

## Tổng Quan

Hệ thống bảo mật đã được cải tiến để khắc phục vấn đề người dùng có thể sử dụng F5 để refresh trang và reset trạng thái cảnh báo. <PERSON><PERSON><PERSON> đây, hệ thống sẽ theo dõi và lưu trữ trạng thái cảnh báo trên server, đảm bảo tính liên tục ngay cả khi người dùng refresh trang.

## Các Tính Năng Mới

### 1. Kh<PERSON><PERSON> Phục Trạng Thái Cảnh Báo
- **L<PERSON>u trữ server-side**: Số lần vi phạm được lưu trong database
- **Khôi phục tự động**: <PERSON><PERSON> trang được tải lạ<PERSON>, hệ thống tự động khôi phục số cảnh báo từ server
- **<PERSON><PERSON><PERSON> thúc ngay lập tức**: <PERSON><PERSON><PERSON> đã đạt giới hạn cảnh báo, bài thi sẽ kết thúc ngay khi trang được tải

### 2. Theo Dõi Refresh Trang
- **Đếm số lần refresh**: Hệ thống theo dõi số lần người dùng refresh trang
- **Cảnh báo dần dần**: 
  - Lần 2: Cảnh báo nhẹ
  - Lần 3: Cảnh báo nghiêm trọng và tính là vi phạm
- **Lưu trữ trong sessionStorage**: Sử dụng sessionStorage để theo dõi qua các lần refresh

### 3. Phát Hiện Nhiều Tab/Cửa Sổ
- **Theo dõi tab hoạt động**: Phát hiện khi người dùng mở nhiều tab cùng lúc
- **Cảnh báo ngay lập tức**: Hiển thị cảnh báo khi phát hiện nhiều tab
- **Theo dõi visibility**: Ghi log khi tab bị ẩn/hiện

### 4. API Endpoints Mới
- `POST /exams-get-warning-state`: Lấy trạng thái cảnh báo hiện tại
- `POST /exams-save-warning-state`: Lưu trạng thái cảnh báo

## Cấu Hình Bảo Mật

```javascript
const SECURITY_CONFIG = {
    WARNING_TIMEOUT: 8000,           // Thời gian hiển thị cảnh báo (8 giây)
    MAX_WARNINGS: 3,                 // Số cảnh báo tối đa
    MAX_PAGE_RELOADS: 3,            // Số lần refresh tối đa
    RELOAD_WARNING_THRESHOLD: 2,     // Ngưỡng bắt đầu cảnh báo refresh
    LOG_IMPORTANCE_LEVELS: {
        HIGH: 5,    // Gửi ngay lập tức
        MEDIUM: 3,  // Gửi trong batch tiếp theo  
        LOW: 1      // Có thể bỏ qua nếu quá nhiều
    }
};
```

## Luồng Hoạt Động

### Khi Trang Được Tải
1. **Khởi tạo Security Engine**
2. **Khôi phục trạng thái từ server** (`restoreWarningState()`)
3. **Theo dõi số lần refresh** (`trackPageReloads()`)
4. **Theo dõi nhiều tab** (`trackMultipleTabs()`)
5. **Kiểm tra giới hạn cảnh báo** - Nếu đã đạt giới hạn → Kết thúc bài thi

### Khi Phát Hiện Vi Phạm
1. **Tăng số cảnh báo** (`this.warningState.warnings++`)
2. **Lưu trạng thái lên server** (`saveWarningState()`)
3. **Hiển thị cảnh báo** với thông tin số cảnh báo còn lại
4. **Kiểm tra giới hạn** - Nếu đạt giới hạn → Kết thúc bài thi

### Khi Refresh Trang
1. **Đếm số lần refresh** trong sessionStorage
2. **Gửi log** về hành vi refresh
3. **Cảnh báo** nếu vượt ngưỡng
4. **Coi là vi phạm** nếu quá giới hạn

## Database Schema

### Bảng `exam_session_logs`
```sql
- id (int, auto_increment)
- user_id (int)
- exam_id (int) 
- exam_result_id (int)
- mouse_data (json)
- keyboard_data (json)
- focus_data (json)
- device_info (json)
- ip_address (varchar)
- user_agent (text)
- event_type (varchar) 
- notes (text)
- suspicious_level (tinyint)
- created_at, updated_at (timestamps)
```

## Các Loại Event Được Theo Dõi

### Vi Phạm Nghiêm Trọng (suspicious_level >= 5)
- `security_devtools_detected`
- `security_auto_click_detected`
- `security_abnormal_mouse_movement`
- `security_excessive_page_reloads`
- `security_multiple_tabs_detected`

### Vi Phạm Trung Bình (suspicious_level 3-4)
- `security_suspicious_page_reloads`
- `security_tab_hidden`

### Thông Tin (suspicious_level 0-2)
- `security_state_restored`
- `security_warning_state_saved`
- `security_tab_visible`

## Cách Triển Khai

### 1. Chạy Migration
```bash
php artisan migrate
```

### 2. Cập Nhật Routes
Routes đã được thêm vào `routes/web.php`:
- `/exams-get-warning-state`
- `/exams-save-warning-state`

### 3. Cập Nhật JavaScript
File `public/js/exam-protection-v3.js` đã được cải tiến với:
- Khôi phục trạng thái cảnh báo
- Theo dõi refresh trang
- Phát hiện nhiều tab

## Lợi Ích

### 1. Bảo Mật Tăng Cường
- **Không thể bypass**: Refresh trang không còn reset được trạng thái cảnh báo
- **Theo dõi toàn diện**: Ghi lại mọi hành vi đáng ngờ
- **Phản ứng nhanh**: Kết thúc bài thi ngay khi phát hiện vi phạm nghiêm trọng

### 2. Trải Nghiệm Người Dùng
- **Thông báo rõ ràng**: Hiển thị số cảnh báo còn lại
- **Cảnh báo dần dần**: Cho người dùng cơ hội sửa lỗi
- **Khôi phục thông minh**: Tự động khôi phục trạng thái khi có sự cố

### 3. Quản Lý Dễ Dàng
- **Log chi tiết**: Ghi lại đầy đủ thông tin vi phạm
- **Phân loại mức độ**: Chia thành các mức độ nguy hiểm khác nhau
- **Báo cáo tự động**: Tự động thông báo cho admin khi có vi phạm nghiêm trọng

## Lưu Ý Quan Trọng

1. **SessionStorage**: Được sử dụng để theo dõi refresh trang, sẽ bị xóa khi đóng browser
2. **Device Fingerprint**: Được sử dụng để xác định thiết bị, giúp phát hiện thay đổi thiết bị
3. **Tương thích**: Hoạt động trên cả desktop và mobile
4. **Performance**: Được tối ưu để không ảnh hưởng đến hiệu suất thi

## Troubleshooting

### Nếu trạng thái không được khôi phục
- Kiểm tra API endpoints có hoạt động không
- Xem log Laravel để tìm lỗi
- Đảm bảo CSRF token được thiết lập đúng

### Nếu phát hiện sai nhiều tab
- Kiểm tra sessionStorage có bị xóa không
- Đảm bảo timestamp được cập nhật đúng
- Xem console log để debug

Hệ thống này đảm bảo tính công bằng và bảo mật cao cho các kỳ thi trực tuyến.
