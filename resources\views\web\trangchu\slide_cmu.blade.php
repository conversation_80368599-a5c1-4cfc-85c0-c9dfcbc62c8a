<style>
    :root {
        --color-bg: #2dcb73;
    }

    .flip-clock-wrapper {
        background: #fff;
        padding: 20px 0 45px;
        margin-bottom: 20px;
        text-align: center;
    }

    .flip-clock-wrapper .countdown {
        margin-right: -5px;
        white-space: nowrap;
        display: flex;
        display: -webkit-flex;
        justify-content: center;
        -webkit-justify-content: center;
    }

    .counter {
        margin-right: 5px;
        position: relative;
        display: flex;
        display: -webkit-flex;
    }

    .counter:after {
        display: inline-block;
        content: attr(title);
        position: absolute;
        top: calc(100% + 15px);
        left: 50%;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
       /* background: var(--color-bg); */
       background: #2dcb73;
        color: #fff;
        padding: 3px 5px;
        font-size: 11px;
        border-radius: 5px;
    }
    .days:after {
       /* background: var(--color-bg); */
       background: #2dcb73;
       
    }
    .hours:after {
       background: #5ba2dc;
       
    }
    .minutes:after {
       background: #9b59b6;
       
    }
    .seconds:after {
       background: #f39c12;
       
    }
    .days:after {
       background: #2dcb73;
       
    }
    .counter .green-btn {
        background: #2dcb73;
        /* background: var(--color-bg); */
        color: #fff;
        padding: 5px;
        font-size: 4rem;
        line-height: 4rem;
        font-weight: bold;
        margin-right: 2px;
        border-radius: 4px;
    }

    .hours .green-btn {
        background: #5ba2dc;
        /* --color-bg: #5ba2dc; */
    }

    .minutes .green-btn{
        background: #9b59b6;
        /* --color-bg: #9b59b6; */
    }

    .seconds .green-btn{
        background: #f39c12;
        /* --color-bg: #f39c12; */
    }

    .countdown-clock .headline {
        font-weight: bold;
        color: #fff;
        text-transform: uppercase;
        font-size: 1.6rem;
        border-bottom: 2px solid rgba(255, 255, 255, 0.8);
        text-align: center;
        padding: 15px;
        margin-top: 0;
        margin-bottom: 20px;
        background: transparent;
    }


    .countdown-clock .button-group {
        display: flex;
        display: -webkit-flex;
        flex-direction: column;
        -webkit-flex-direction: column;
    }

    .countdown-clock .button-group .btn {
        max-width: 225px;
        width: 100%;
        margin: auto;
        margin-bottom: 20px;
        border-radius: 999px;
        text-transform: uppercase;
    }

    .countdown-clock .button-group .btn {
        max-width: 225px;
        width: 100%;
        margin: auto;
        margin-bottom: 20px;
        border-radius: 999px;
        text-transform: uppercase;
    }
    @media (max-width: 1024px){
        .countdown-clock .button-group{
            flex-direction: row;
            -webkit-flex-direction: row;
            justify-content: center;
            -webkit-justify-content: center;
        }
        .button-group .btn-default{
            margin-left: 15px!important;
            margin-right: 8px!important;
        }
        .button-group .btn-danger{
            margin-right: 15px!important;
             margin-left: 8px!important;
        }
        .countdown-clock .button-group .btn{
            font-size: 14px;
            margin-bottom: 15px;
        }
        .btn-group-lg>.btn, .btn-lg{
            padding: 6px 10px;
        }
    }
    @media (max-width: 480px){
        .countdown-clock .headline {
            padding: 4px;
        }
        .countdown-clock .mb-15.text-center{
            padding-bottom: 8px;
        }
        
        .flip-clock-wrapper{
            margin-bottom: 15px;
        }
        .flip-clock-wrapper {
            padding: 10px 0 45px;
        }
    }

    @media(max-width: 543px) {
        .counter .green-btn {
            font-size: 3.2rem;
        }
    }

    .contest-banner {
        max-width: 1200px;
        margin: 0 auto;
        padding: 30px;  /* Tăng padding */
        text-align: center;
        background: rgba(255, 249, 230, 0.95);  /* Tăng độ đậm của nền */
        position: relative;
        font-family: 'Arial', sans-serif;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);  /* Thêm shadow cho banner */
    }

    .contest-banner::before {
        content: '';
        position: absolute;
        max-width: 1200px;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('/css/images/trong-dong.png') no-repeat center center;
        background-size: cover;
        filter: opacity(15%);  /* Giảm opacity để text dễ đọc hơn */
    }

    .contest-banner > * {
        position: relative;
        z-index: 2;
    }

    .header-title {
        color: #d4201e;
        font-size: 26px;  /* Tăng size */
        font-weight: bold;
        margin-bottom: 25px;
        line-height: 1.5;
        /*text-transform: uppercase;*/
        letter-spacing: 0.5px;  /* Tăng khoảng cách chữ */
        text-shadow: 1px 1px 0px rgba(255, 255, 255, 0.8);  /* Thêm text shadow */
    }

    .logo-container {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40px;  /* Tăng khoảng cách giữa các logo */
        margin: 30px 0;
        padding: 15px;
    }

    .logo {
        width: 75px;
        height: 75px;
        transition: transform 0.3s ease;  /* Thêm animation */
    }

    .logo:hover {
        transform: scale(1.1);
    }

    .contest-title {
        color: #006241;
        font-size: 36px;  /* Tăng size */
        font-weight: bold;
        margin: 30px 0;
        letter-spacing: 1px;
        text-shadow: 2px 2px 0px rgba(255, 255, 255, 0.8);
    }

    .contest-subtitle {
        color: #d4201e;
        font-size: 36px;
        font-weight: bold;
        line-height: 1.5;
        margin: 25px 0;
        text-shadow: 1px 1px 0px rgba(255, 255, 255, 0.8);
    }

    .time-section {
        display: inline-flex;
        align-items: center;
        background: transparent;
        margin: 25px auto;
        position: relative;
    }

    /* Style cho tab THỜI GIAN */
    .time-label {
        background: #4a8c3f;  /* Màu xanh lá */
        color: white;
        padding: 5px 15px;
        display: flex;
        align-items: center;
        gap: 5px;
        border-radius: 5px 0 0 5px;
        position: relative;
        font-weight: bold;
        height: 50px;
    }

    /* Icon đồng hồ */
    .clock-icon {
        width: 18px;
        height: 18px;
    }

    /* Phần nội dung thời gian */
    .time-range {
        background: #f5f5f5;  /* Màu xám nhạt */
        padding: 5px 20px;
        border: 2px solid #4a8c3f;  /* Viền xanh */
        border-radius: 0 5px 5px 0;
        color: #d4201e;  /* Màu đỏ cho text */
        font-style: italic;
        height: 50px;
        display: flex;
        align-items: center;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .time-section {
            flex-direction: column;
            width: 100%;
        }

        .time-label {
            border-radius: 5px 5px 0 0;
            justify-content: center;
            width: 100%;
        }

        .time-range {
            border-radius: 0 0 5px 5px;
            justify-content: center;
            width: 100%;
            text-align: center;
        }
    }

    .participate-section {
        background: #f39c12; /* Màu đỏ */
        color: white;
        padding: 15px;
        margin: 20px -30px -30px -30px; /* Margin âm bằng với padding của contest-banner */
        text-align: center;
    }

    .contest-link {
        color: white;
        text-decoration: none;
        font-weight: 500;
        font-family: Arial, sans-serif;
    }

    .contest-link:hover {
        text-decoration: underline;
    }

    /* Điều chỉnh responsive */
    @media (max-width: 768px) {
        .participate-section {
            margin: 15px -15px -15px -15px; /* Tương ứng với padding mobile của contest-banner */
        }
    }

    @media (max-width: 768px) {
        .contest-banner {
            padding: 20px 15px;
        }

        .header-title {
            font-size: 22px;
        }

        .contest-title {
            font-size: 32px;
        }

        .contest-subtitle {
            font-size: 26px;
        }

        .logo-container {
            gap: 20px;
            flex-wrap: wrap;
            padding: 10px;
        }

        .logo {
            width: 65px;
            height: 65px;
        }

        .time-label {
            font-size: 18px;
            padding: 8px 15px;
        }

        .time-range {
            font-size: 16px;
        }
    }
</style>
<div class="ModuleWrapper">
    <section class="group-event-exam-countdown">
    <div class="gradient-overlay"></div>

        <div class="center-drum">
            <div class="rotating-drum"></div>
        </div>
    
    <div class="light-beam"></div>
        <div class="container">
            <div class="slide-wrapper">
                <div class="row">
                    <div class="col-sm-12 col-md-8">
                        <div class="contest-banner">
                            <div class="header-title">
                                <!-- CUỘC THI TRỰC TUYẾN -->
                            </div>
                            <div class="contest-title">
                                CUỘC THI TRỰC TUYẾN
                            </div>

                            @if(isset($examIsActive))
                                <div class="contest-title">
                                    
                                </div>

                                <div class="contest-subtitle">
                                    {{ $examIsActive->name }}
                                </div>

                                <div class="time-section">
                                    <div class="time-label">
                                        <img src="/css/images/clock-icon.png" alt="Clock" class="clock-icon"> THỜI GIAN
                                    </div>
                                    <div class="time-range">
                                        Từ {{ \Carbon\Carbon::parse($examIsActive->date_start)->format('H \g\i\ờ i \p\h\ú\t \n\g\à\y d/m/Y') }}
                                        đến {{ \Carbon\Carbon::parse($examIsActive->date_end)->format('H \g\i\ờ i \p\h\ú\t \n\g\à\y d/m/Y') }}
                                    </div>
                                </div>
                            @else
                                <div class="contest-title" style="color: #d4201e;">
                                    THÔNG BÁO
                                </div>

                                <div class="alert alert-warning" style="margin: 20px; text-align: center;">
                                    <h4><i class="fa fa-exclamation-triangle"></i> Hiện tại chưa có cuộc thi nào được tổ chức</h4>
                                    <p>Vui lòng quay lại sau để tham gia các cuộc thi sắp tới.</p>
                                </div>
                            @endif

                            <div class="participate-section">
                                Tham gia trực tuyến tại:<br>
                                <a href="https://timhieuphapluat.camau.gov.vn" class="contest-link">https://timhieuphapluat.camau.gov.vn</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-4 countdown-left">
                        <div id="module16" class="ModuleWrapper">
                            <section class="countdown-clock">
                                <h2 class="headline"></h2>
                                <div style="text-transform: uppercase;" class="mb-15 text-center">Thời gian còn lại của cuộc thi</div>
                                <div class="flip-clock-wrapper">
                                    <span class="countdown clock" data-time="0">

                                    </span>
                                </div>
                                <div class="counting-user">Đã có {{$numberOfTest}} lượt dự thi</div>
                                <div class="button-group">
                                    <a class="btn btn-lg btn-default" href="{{url('about/the-le')}}" title="Thể lệ cuộc thi">Thể lệ</a>
                                    @if(isset($examIsActive))
                                    <a class="btn btn-lg btn-danger" data-type="notStart" href="{{url('exams')}}" title="Vào thi">Vào thi</a>
                                    @else
                                    <a class="btn btn-lg btn-danger" data-type="notStart" href="javascript:void(0)" title="Chưa có cuộc thi nào">Chưa có cuộc thi nào</a>
                                    @endif
                                    
                                </div>
                            </section>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<input type="hidden" id="time_current" value="{{$time_current}}" />
@php
$countDownDate=0;
if(isset($examIsActive) ){
$countDownDate = $examIsActive->date_end;
}
@endphp
@push('scripts')
<script>
    $(function() {
        var sDatesNull = '<span class="days counter" title="Ngày">' +
            '<span class="green-btn">0</span>' +
            '<span class="green-btn">0</span>' +
            '</span>' +
            '<span class="hours counter" title="Giờ">' +
            '<span class="green-btn">0</span>' +
            '<span class="green-btn">0</span>' +
            '</span>' +
            '<span class="minutes counter" title="Phút">' +
            '<span class="green-btn">0</span>' +
            '<span class="green-btn">0</span>' +
            '</span>' +
            '<span class="seconds counter" title="Giây">' +
            '<span class="green-btn">0</span>' +
            '<span class="green-btn">0</span>' +
            '</span> ';
        var count_date = "{{$count_date}}";
        if (count_date == '') {
            $('.countdown').html(sDatesNull);
        } else {
            var time_current = Number($('#time_current').val());
            var x = setInterval(function() {
                var distance = count_date - time_current;
                time_current = time_current + 1000;
                // var distance = countDownDate - now;
                // Time calculations for days, hours, minutes and seconds
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                var seconds = Math.floor((distance % (1000 * 60)) / 1000);
                var sDays = '';
                var sHtmlTime = '';
                // Output the result in an element with id="demo"
                sHtmlTime += convertStringTime(days, 'day');
                sHtmlTime += convertStringTime(hours, 'hours');
                sHtmlTime += convertStringTime(minutes, 'minutes');
                sHtmlTime += convertStringTime(seconds, 'seconds');
                $('.countdown').html(sHtmlTime);
                // If the count down is over, write some text 
                if (distance < 0) {
                    clearInterval(x);
                    $(".countdown").html(sDatesNull);
                }
            }, 1000);
        }
    });
</script>
@endpush